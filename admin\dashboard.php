<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in and has admin role
requireRole(['super_admin', 'office_admin']);

// Get dashboard statistics
$stats = [];

// Total clients
$db->query("SELECT COUNT(*) as count FROM clients WHERE is_active = 1");
$stats['total_clients'] = $db->single()->count;

// Total cylinders
$db->query("SELECT COUNT(*) as count FROM cylinders WHERE is_active = 1");
$stats['total_cylinders'] = $db->single()->count;

// Available cylinders
$db->query("SELECT COUNT(*) as count FROM cylinders WHERE status = 'available' AND is_active = 1");
$stats['available_cylinders'] = $db->single()->count;

// Pending orders
$db->query("SELECT COUNT(*) as count FROM orders WHERE status IN ('pending', 'assigned', 'refilling')");
$stats['pending_orders'] = $db->single()->count;

// Today's orders
$db->query("SELECT COUNT(*) as count FROM orders WHERE DATE(created_at) = CURDATE()");
$stats['today_orders'] = $db->single()->count;

// Overdue invoices
$db->query("SELECT COUNT(*) as count FROM invoices WHERE status = 'overdue'");
$stats['overdue_invoices'] = $db->single()->count;

// Get recent orders
$db->query("SELECT o.*, c.name as client_name, g.name as gas_name 
           FROM orders o 
           JOIN clients c ON o.client_id = c.id 
           JOIN gas_types g ON o.gas_type_id = g.id 
           ORDER BY o.created_at DESC LIMIT 5");
$recent_orders = $db->resultset();

// Get tank levels
$db->query("SELECT t.*, g.name as gas_name, l.name as location_name,
           ROUND((t.current_level / t.capacity) * 100, 2) as level_percentage
           FROM tanks t 
           JOIN gas_types g ON t.gas_type_id = g.id 
           JOIN locations l ON t.location_id = l.id 
           WHERE t.is_active = 1 
           ORDER BY level_percentage ASC");
$tanks = $db->resultset();

$page_title = 'Admin Dashboard';
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-tachometer-alt"></i> Dashboard</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-download"></i> Export
                        </button>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        Total Clients
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo $stats['total_clients']; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-users fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        Available Cylinders
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo $stats['available_cylinders']; ?> / <?php echo $stats['total_cylinders']; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-fire-extinguisher fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        Pending Orders
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo $stats['pending_orders']; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        Today's Orders
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo $stats['today_orders']; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Tank Levels -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-gas-pump"></i> Tank Levels
                            </h6>
                        </div>
                        <div class="card-body">
                            <?php foreach ($tanks as $tank): ?>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                        <span class="small font-weight-bold">
                                            <?php echo $tank->gas_name; ?> - <?php echo $tank->location_name; ?>
                                        </span>
                                        <span class="small"><?php echo $tank->level_percentage; ?>%</span>
                                    </div>
                                    <div class="progress" style="height: 10px;">
                                        <div class="progress-bar <?php echo $tank->level_percentage < 20 ? 'bg-danger' : ($tank->level_percentage < 50 ? 'bg-warning' : 'bg-success'); ?>" 
                                             role="progressbar" 
                                             style="width: <?php echo $tank->level_percentage; ?>%">
                                        </div>
                                    </div>
                                    <small class="text-muted">
                                        <?php echo $tank->current_level; ?> / <?php echo $tank->capacity; ?> <?php echo $tank->unit; ?>
                                    </small>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>

                <!-- Recent Orders -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-list"></i> Recent Orders
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Order #</th>
                                            <th>Client</th>
                                            <th>Gas</th>
                                            <th>Qty</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_orders as $order): ?>
                                            <tr>
                                                <td><?php echo $order->order_number; ?></td>
                                                <td><?php echo $order->client_name; ?></td>
                                                <td><?php echo $order->gas_name; ?></td>
                                                <td><?php echo $order->quantity; ?></td>
                                                <td>
                                                    <span class="badge bg-<?php 
                                                        echo $order->status == 'pending' ? 'warning' : 
                                                            ($order->status == 'dispatched' ? 'success' : 'info'); 
                                                    ?>">
                                                        <?php echo ucfirst($order->status); ?>
                                                    </span>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-center">
                                <a href="orders.php" class="btn btn-sm btn-primary">View All Orders</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-bolt"></i> Quick Actions
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-2">
                                    <a href="orders/create.php" class="btn btn-primary btn-block">
                                        <i class="fas fa-plus"></i> New Order
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <a href="clients/create.php" class="btn btn-success btn-block">
                                        <i class="fas fa-user-plus"></i> Add Client
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <a href="cylinders/create.php" class="btn btn-info btn-block">
                                        <i class="fas fa-fire-extinguisher"></i> Add Cylinder
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <a href="invoices/generate.php" class="btn btn-warning btn-block">
                                        <i class="fas fa-file-invoice"></i> Generate Invoice
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
