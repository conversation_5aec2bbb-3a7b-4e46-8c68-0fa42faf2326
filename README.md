# 🏭 Industrial Gas Cylinder Management System

> **Developed for:** Sony Enterprises  
> **By:** Page Perfect Tech  
> **Platform:** Core PHP Web Application (Mobile-Friendly)

## 🚀 Quick Start

### Prerequisites
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx)
- Modern web browser

### Installation Steps

1. **Clone/Download the project**
   ```bash
   git clone <repository-url>
   cd gas-cylinder-management-system
   ```

2. **Set up web server**
   - Place the project in your web server's document root
   - Ensure PHP has write permissions to the project directory

3. **Run the setup wizard**
   - Open your browser and navigate to: `http://your-domain/install/setup.php`
   - Fill in the database and company information
   - Click "Setup Database" to complete installation

4. **Login to the system**
   - Navigate to: `http://your-domain/auth/login.php`
   - Use the admin credentials you created during setup

## 🔐 Default Login Credentials

After setup, use the credentials you created. If you need to reset:

**Super Admin:**
- Username: `admin` (or what you set during setup)
- Password: (what you set during setup)

**Default Client Login:**
- Phone/Email/Client Code: (as registered)
- Default Password: `1234` (must be changed on first login)

## 📋 System Features

### ✅ Completed Features

1. **Authentication System**
   - Role-based login (Staff/Admin/Client)
   - Secure password hashing
   - Session management
   - Force password change on first login

2. **Database Structure**
   - Complete schema for all modules
   - Proper relationships and indexes
   - Sample data included

3. **Admin Dashboard**
   - Statistics overview
   - Tank level monitoring
   - Recent orders display
   - Quick action buttons

4. **Responsive Design**
   - Bootstrap 5 framework
   - Mobile-friendly interface
   - Modern UI with animations
   - Print-friendly layouts

### 🚧 In Development

1. **Client Management Module**
2. **Cylinder Management Module**
3. **Tank Management Module**
4. **Order & Refill Workflow**
5. **Invoicing System**
6. **Payment Management**
7. **WhatsApp Notifications**
8. **Reports & Analytics**

## 🏗️ Project Structure

```
gas-cylinder-management-system/
├── admin/                  # Admin panel files
│   ├── dashboard.php      # Main admin dashboard
│   ├── clients/           # Client management
│   ├── cylinders/         # Cylinder management
│   ├── orders/            # Order management
│   └── settings/          # System settings
├── auth/                  # Authentication files
│   ├── login.php         # Login page
│   └── logout.php        # Logout handler
├── client/               # Client portal files
├── staff/                # Staff interface files
├── accounts/             # Accounting module
├── reports/              # Reports module
├── config/               # Configuration files
│   └── database.php      # Database configuration
├── includes/             # Common includes
│   ├── functions.php     # Common functions
│   ├── header.php        # Page header
│   ├── sidebar.php       # Navigation sidebar
│   └── footer.php        # Page footer
├── database/             # Database files
│   └── schema.sql        # Database schema
├── install/              # Installation files
│   └── setup.php         # Setup wizard
├── assets/               # Static assets
│   ├── css/              # Custom stylesheets
│   ├── js/               # Custom JavaScript
│   └── images/           # Images and logos
└── index.php             # Main entry point
```

## 🔧 Configuration

### Database Configuration
Edit `config/database.php` to update database settings:

```php
define('DB_HOST', 'localhost');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
define('DB_NAME', 'gas_cylinder_management');
```

### Company Settings
Update company information through the admin panel:
- Admin → Settings → Company Settings

## 🎯 User Roles & Permissions

| Role | Permissions |
|------|-------------|
| **Super Admin** | Full system access, user management, settings |
| **Office Admin** | Orders, invoices, client management, reports |
| **Refilling Staff** | View assigned orders, scan cylinders, mark filled |
| **Loading Staff** | Confirm loading, scan cylinders |
| **Accountant** | Payments, expenses, financial reports |
| **Client** | View orders, track cylinders, download invoices |

## 📱 Mobile Features

- **QR/Barcode Scanner**: Camera-based scanning for cylinders
- **Responsive Design**: Works on all device sizes
- **Touch-Friendly**: Optimized for mobile interaction
- **Offline Capability**: Basic functionality without internet

## 🔔 WhatsApp Integration

The system includes WhatsApp Web integration for:
- Order notifications
- Invoice sharing
- Payment reminders
- Cylinder return alerts

## 📊 Reports Available

- Cylinder Register
- Wastage Summary
- Tank Usage Logs
- Profit & Loss Statement
- Customer Ledger
- GST/Invoice Register
- Inspection & Expiry Alerts

## 🛠️ Technical Stack

- **Backend**: Core PHP 7.4+
- **Database**: MySQL 5.7+
- **Frontend**: Bootstrap 5, jQuery
- **Charts**: Chart.js
- **QR/Barcode**: HTML5-QRCode
- **Icons**: Font Awesome 6
- **Tables**: DataTables

## 🔒 Security Features

- Password hashing with PHP's `password_hash()`
- SQL injection prevention with PDO prepared statements
- XSS protection with input sanitization
- Session security with proper timeout
- Role-based access control
- Activity logging

## 📞 Support

For technical support or customization requests:

**Page Perfect Tech**
- Email: <EMAIL>
- Phone: +91-XXXXXXXXXX

**Sony Enterprises**
- Contact your system administrator

## 📄 License

This software is developed exclusively for Sony Enterprises by Page Perfect Tech. All rights reserved.

## 🔄 Version History

- **v1.0.0** - Initial setup with authentication and dashboard
- **v1.1.0** - Client management module (In Development)
- **v1.2.0** - Cylinder management module (Planned)
- **v1.3.0** - Order workflow (Planned)
- **v2.0.0** - Complete system with all modules (Planned)

---

> **Note**: This is a work in progress. New modules will be added incrementally based on priority and requirements.
