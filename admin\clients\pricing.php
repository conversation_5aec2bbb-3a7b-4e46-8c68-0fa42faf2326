<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Check if user is logged in and has admin role
requireRole(['super_admin', 'office_admin']);

$client_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$errors = [];
$success = '';

if (!$client_id) {
    setFlashMessage('error', 'Invalid client ID.');
    header('Location: index.php');
    exit();
}

// Get client details
$db->query("SELECT * FROM clients WHERE id = :id");
$db->bind(':id', $client_id);
$client = $db->single();

if (!$client) {
    setFlashMessage('error', 'Client not found.');
    header('Location: index.php');
    exit();
}

// Handle form submission for adding/updating pricing
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        if ($_POST['action'] == 'add_pricing') {
            $gas_type_id = (int)$_POST['gas_type_id'];
            $special_price = (float)$_POST['special_price'];
            $effective_from = $_POST['effective_from'];
            $effective_to = !empty($_POST['effective_to']) ? $_POST['effective_to'] : null;
            
            // Validation
            if (empty($gas_type_id)) {
                $errors[] = 'Please select a gas type.';
            }
            
            if (empty($special_price) || $special_price <= 0) {
                $errors[] = 'Please enter a valid special price.';
            }
            
            if (empty($effective_from)) {
                $errors[] = 'Please select effective from date.';
            }
            
            // Check if pricing already exists for this gas type and date range
            if (empty($errors)) {
                $db->query("SELECT id FROM client_pricing 
                           WHERE client_id = :client_id AND gas_type_id = :gas_type_id 
                           AND is_active = 1 AND (effective_to IS NULL OR effective_to >= :effective_from)");
                $db->bind(':client_id', $client_id);
                $db->bind(':gas_type_id', $gas_type_id);
                $db->bind(':effective_from', $effective_from);
                if ($db->single()) {
                    $errors[] = 'Special pricing already exists for this gas type in the selected date range.';
                }
            }
            
            if (empty($errors)) {
                try {
                    $db->query("INSERT INTO client_pricing (client_id, gas_type_id, special_price, effective_from, effective_to, created_by, created_at) 
                               VALUES (:client_id, :gas_type_id, :special_price, :effective_from, :effective_to, :created_by, NOW())");
                    
                    $db->bind(':client_id', $client_id);
                    $db->bind(':gas_type_id', $gas_type_id);
                    $db->bind(':special_price', $special_price);
                    $db->bind(':effective_from', $effective_from);
                    $db->bind(':effective_to', $effective_to);
                    $db->bind(':created_by', $_SESSION['user_id']);
                    
                    $db->execute();
                    
                    // Update client special pricing flag
                    $db->query("UPDATE clients SET special_pricing = 1 WHERE id = :id");
                    $db->bind(':id', $client_id);
                    $db->execute();
                    
                    logActivity($_SESSION['user_id'], 'client_pricing_added', "Added special pricing for client: {$client->name}");
                    
                    setFlashMessage('success', 'Special pricing added successfully!');
                    header('Location: pricing.php?id=' . $client_id);
                    exit();
                    
                } catch (Exception $e) {
                    $errors[] = 'Error adding pricing: ' . $e->getMessage();
                }
            }
        } elseif ($_POST['action'] == 'deactivate_pricing') {
            $pricing_id = (int)$_POST['pricing_id'];
            
            try {
                $db->query("UPDATE client_pricing SET is_active = 0 WHERE id = :id AND client_id = :client_id");
                $db->bind(':id', $pricing_id);
                $db->bind(':client_id', $client_id);
                $db->execute();
                
                logActivity($_SESSION['user_id'], 'client_pricing_deactivated', "Deactivated special pricing for client: {$client->name}");
                
                setFlashMessage('success', 'Special pricing deactivated successfully!');
                header('Location: pricing.php?id=' . $client_id);
                exit();
                
            } catch (Exception $e) {
                $errors[] = 'Error deactivating pricing: ' . $e->getMessage();
            }
        }
    }
}

// Get gas types
$db->query("SELECT * FROM gas_types WHERE is_active = 1 ORDER BY name");
$gas_types = $db->resultset();

// Get current special pricing
$db->query("SELECT cp.*, g.name as gas_name, u.full_name as created_by_name 
           FROM client_pricing cp 
           JOIN gas_types g ON cp.gas_type_id = g.id 
           LEFT JOIN users u ON cp.created_by = u.id 
           WHERE cp.client_id = :client_id 
           ORDER BY cp.is_active DESC, cp.effective_from DESC");
$db->bind(':client_id', $client_id);
$special_pricing = $db->resultset();

$page_title = 'Special Pricing - ' . $client->name;
include '../../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../../includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-tags"></i> Special Pricing</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="view.php?id=<?php echo $client->id; ?>" class="btn btn-sm btn-info">
                            <i class="fas fa-eye"></i> View Client
                        </a>
                        <a href="edit.php?id=<?php echo $client->id; ?>" class="btn btn-sm btn-warning">
                            <i class="fas fa-edit"></i> Edit Client
                        </a>
                    </div>
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Clients
                    </a>
                </div>
            </div>

            <!-- Client Info -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h5><?php echo htmlspecialchars($client->name); ?> 
                                <span class="badge bg-primary"><?php echo $client->client_code; ?></span>
                            </h5>
                            <p class="text-muted mb-0">
                                <?php echo htmlspecialchars($client->company_name ?? ''); ?> | 
                                <?php echo $client->phone; ?> | 
                                <?php echo htmlspecialchars($client->email ?? ''); ?>
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <span class="badge bg-<?php echo $client->special_pricing ? 'success' : 'secondary'; ?> fs-6">
                                Special Pricing <?php echo $client->special_pricing ? 'Enabled' : 'Disabled'; ?>
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <div class="row">
                <!-- Add New Pricing -->
                <div class="col-lg-4">
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Add Special Pricing</h6>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="">
                                <input type="hidden" name="action" value="add_pricing">
                                
                                <div class="mb-3">
                                    <label for="gas_type_id" class="form-label">Gas Type <span class="text-danger">*</span></label>
                                    <select class="form-select" id="gas_type_id" name="gas_type_id" required>
                                        <option value="">Select Gas Type</option>
                                        <?php foreach ($gas_types as $gas_type): ?>
                                            <option value="<?php echo $gas_type->id; ?>">
                                                <?php echo $gas_type->name; ?> (<?php echo $gas_type->code; ?>)
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="special_price" class="form-label">Special Price (₹) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="special_price" name="special_price" 
                                           step="0.01" min="0" required>
                                </div>

                                <div class="mb-3">
                                    <label for="effective_from" class="form-label">Effective From <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="effective_from" name="effective_from" 
                                           value="<?php echo date('Y-m-d'); ?>" required>
                                </div>

                                <div class="mb-3">
                                    <label for="effective_to" class="form-label">Effective To</label>
                                    <input type="date" class="form-control" id="effective_to" name="effective_to">
                                    <div class="form-text">Leave empty for ongoing pricing</div>
                                </div>

                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-plus"></i> Add Pricing
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Current Pricing -->
                <div class="col-lg-8">
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Current Special Pricing</h6>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($special_pricing)): ?>
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Gas Type</th>
                                                <th>Special Price</th>
                                                <th>Effective From</th>
                                                <th>Effective To</th>
                                                <th>Status</th>
                                                <th>Created By</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($special_pricing as $pricing): ?>
                                            <tr class="<?php echo !$pricing->is_active ? 'table-secondary' : ''; ?>">
                                                <td><?php echo $pricing->gas_name; ?></td>
                                                <td><strong><?php echo formatCurrency($pricing->special_price); ?></strong></td>
                                                <td><?php echo formatDate($pricing->effective_from); ?></td>
                                                <td>
                                                    <?php 
                                                    if ($pricing->effective_to) {
                                                        echo formatDate($pricing->effective_to);
                                                        if (strtotime($pricing->effective_to) < time()) {
                                                            echo ' <span class="badge bg-warning">Expired</span>';
                                                        }
                                                    } else {
                                                        echo '<span class="badge bg-success">Ongoing</span>';
                                                    }
                                                    ?>
                                                </td>
                                                <td>
                                                    <?php if ($pricing->is_active): ?>
                                                        <span class="badge bg-success">Active</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">Inactive</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo htmlspecialchars($pricing->created_by_name ?? '-'); ?></td>
                                                <td>
                                                    <?php if ($pricing->is_active): ?>
                                                        <form method="POST" style="display: inline;">
                                                            <input type="hidden" name="action" value="deactivate_pricing">
                                                            <input type="hidden" name="pricing_id" value="<?php echo $pricing->id; ?>">
                                                            <button type="submit" class="btn btn-sm btn-danger" 
                                                                    onclick="return confirm('Are you sure you want to deactivate this pricing?')">
                                                                <i class="fas fa-times"></i> Deactivate
                                                            </button>
                                                        </form>
                                                    <?php else: ?>
                                                        <span class="text-muted">Deactivated</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-tags fa-3x mb-3"></i>
                                    <h5>No Special Pricing Set</h5>
                                    <p>This client doesn't have any special pricing configured yet.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Standard Pricing Reference -->
                    <div class="card shadow mt-4">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Standard Pricing Reference</h6>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i> 
                                <strong>Note:</strong> Standard pricing will be used for gas types without special pricing.
                            </div>
                            
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Gas Type</th>
                                            <th>Standard Price</th>
                                            <th>Special Price</th>
                                            <th>Savings</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($gas_types as $gas_type): ?>
                                        <?php 
                                        $special_price = getClientPrice($client_id, $gas_type->id);
                                        $standard_price = 500; // This should come from a settings table
                                        ?>
                                        <tr>
                                            <td><?php echo $gas_type->name; ?></td>
                                            <td><?php echo formatCurrency($standard_price); ?></td>
                                            <td>
                                                <?php if ($special_price): ?>
                                                    <strong class="text-success"><?php echo formatCurrency($special_price); ?></strong>
                                                <?php else: ?>
                                                    <span class="text-muted">Not Set</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($special_price): ?>
                                                    <?php $savings = $standard_price - $special_price; ?>
                                                    <span class="<?php echo $savings > 0 ? 'text-success' : 'text-danger'; ?>">
                                                        <?php echo formatCurrency($savings); ?>
                                                        (<?php echo round(($savings / $standard_price) * 100, 1); ?>%)
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
// Set minimum date for effective_from to today
document.getElementById('effective_from').min = new Date().toISOString().split('T')[0];

// Set minimum date for effective_to based on effective_from
document.getElementById('effective_from').addEventListener('change', function() {
    document.getElementById('effective_to').min = this.value;
});
</script>

<?php include '../../includes/footer.php'; ?>
