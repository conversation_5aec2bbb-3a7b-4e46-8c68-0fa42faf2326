<?php
// Common functions for Gas Cylinder Management System

// Sanitize input data
function sanitize($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// Generate random password
function generatePassword($length = 8) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}

// Hash password
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

// Verify password
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// Check if user is logged in
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

// Check user role
function hasRole($required_role) {
    if (!isLoggedIn()) {
        return false;
    }
    
    $user_role = $_SESSION['user_role'];
    
    // Super admin has access to everything
    if ($user_role === 'super_admin') {
        return true;
    }
    
    // Check specific role
    if (is_array($required_role)) {
        return in_array($user_role, $required_role);
    }
    
    return $user_role === $required_role;
}

// Redirect if not authorized
function requireRole($required_role) {
    if (!hasRole($required_role)) {
        header('Location: ../auth/login.php?error=unauthorized');
        exit();
    }
}

// Generate unique cylinder code
function generateCylinderCode($gas_type, $size) {
    $prefix = strtoupper(substr($gas_type, 0, 2));
    $size_code = str_pad($size, 2, '0', STR_PAD_LEFT);
    $random = str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
    return $prefix . $size_code . $random;
}

// Generate QR code data
function generateQRData($type, $id, $additional_data = []) {
    $data = [
        'type' => $type,
        'id' => $id,
        'timestamp' => time()
    ];
    
    if (!empty($additional_data)) {
        $data = array_merge($data, $additional_data);
    }
    
    return base64_encode(json_encode($data));
}

// Format currency
function formatCurrency($amount) {
    return '₹' . number_format($amount, 2);
}

// Format date
function formatDate($date, $format = 'd-m-Y') {
    return date($format, strtotime($date));
}

// Format datetime
function formatDateTime($datetime, $format = 'd-m-Y H:i:s') {
    return date($format, strtotime($datetime));
}

// Calculate cylinder age in years
function calculateCylinderAge($manufacture_date) {
    $manufacture = new DateTime($manufacture_date);
    $now = new DateTime();
    $age = $now->diff($manufacture);
    return $age->y;
}

// Check if cylinder needs inspection
function needsInspection($last_inspection_date, $inspection_interval_years = 5) {
    if (empty($last_inspection_date)) {
        return true;
    }
    
    $last_inspection = new DateTime($last_inspection_date);
    $next_inspection = $last_inspection->add(new DateInterval('P' . $inspection_interval_years . 'Y'));
    $now = new DateTime();
    
    return $now >= $next_inspection;
}

// Generate WhatsApp message URL
function generateWhatsAppURL($phone, $message) {
    $phone = preg_replace('/[^0-9]/', '', $phone);
    if (substr($phone, 0, 2) !== '91') {
        $phone = '91' . $phone;
    }
    
    $encoded_message = urlencode($message);
    return "https://wa.me/{$phone}?text={$encoded_message}";
}

// Log activity
function logActivity($user_id, $action, $details = '') {
    global $db;
    
    $db->query("INSERT INTO activity_logs (user_id, action, details, created_at) VALUES (:user_id, :action, :details, NOW())");
    $db->bind(':user_id', $user_id);
    $db->bind(':action', $action);
    $db->bind(':details', $details);
    $db->execute();
}

// Send notification (placeholder for WhatsApp integration)
function sendNotification($phone, $message, $type = 'general') {
    // This will be implemented with WhatsApp Web integration
    // For now, we'll log the notification
    global $db;
    
    $db->query("INSERT INTO notifications (phone, message, type, status, created_at) VALUES (:phone, :message, :type, 'pending', NOW())");
    $db->bind(':phone', $phone);
    $db->bind(':message', $message);
    $db->bind(':type', $type);
    $db->execute();
    
    return true;
}

// Get company settings
function getCompanySettings() {
    global $db;
    
    $db->query("SELECT * FROM company_settings WHERE id = 1");
    return $db->single();
}

// Flash message functions
function setFlashMessage($type, $message) {
    $_SESSION['flash_message'] = [
        'type' => $type,
        'message' => $message
    ];
}

function getFlashMessage() {
    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message'];
        unset($_SESSION['flash_message']);
        return $message;
    }
    return null;
}

// Pagination helper
function paginate($total_records, $records_per_page, $current_page) {
    $total_pages = ceil($total_records / $records_per_page);
    $offset = ($current_page - 1) * $records_per_page;
    
    return [
        'total_pages' => $total_pages,
        'current_page' => $current_page,
        'offset' => $offset,
        'limit' => $records_per_page,
        'has_previous' => $current_page > 1,
        'has_next' => $current_page < $total_pages
    ];
}
?>
