<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: ../index.php');
    exit();
}

$error = '';
$success = '';

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = sanitize($_POST['username']);
    $password = $_POST['password'];
    $user_type = $_POST['user_type'];
    
    if (empty($username) || empty($password)) {
        $error = 'Please enter both username and password.';
    } else {
        if ($user_type === 'staff') {
            // Staff/Admin login
            $db->query("SELECT * FROM users WHERE username = :username AND is_active = 1");
            $db->bind(':username', $username);
            $user = $db->single();
            
            if ($user && verifyPassword($password, $user->password)) {
                // Set session variables
                $_SESSION['user_id'] = $user->id;
                $_SESSION['username'] = $user->username;
                $_SESSION['full_name'] = $user->full_name;
                $_SESSION['user_role'] = $user->role;
                $_SESSION['user_type'] = 'staff';
                
                // Update last login
                $db->query("UPDATE users SET last_login = NOW() WHERE id = :id");
                $db->bind(':id', $user->id);
                $db->execute();
                
                // Log activity
                logActivity($user->id, 'login', 'Staff login successful');
                
                // Check if password change is required
                if ($user->force_password_change) {
                    header('Location: change_password.php');
                } else {
                    header('Location: ../index.php');
                }
                exit();
            } else {
                $error = 'Invalid username or password.';
            }
        } else {
            // Client login
            $db->query("SELECT * FROM clients WHERE (phone = :username OR email = :username OR client_code = :username) AND is_active = 1");
            $db->bind(':username', $username);
            $client = $db->single();
            
            if ($client && verifyPassword($password, $client->password)) {
                // Set session variables
                $_SESSION['user_id'] = $client->id;
                $_SESSION['username'] = $client->client_code;
                $_SESSION['full_name'] = $client->name;
                $_SESSION['user_role'] = 'client';
                $_SESSION['user_type'] = 'client';
                
                // Update last login
                $db->query("UPDATE clients SET last_login = NOW() WHERE id = :id");
                $db->bind(':id', $client->id);
                $db->execute();
                
                // Check if password change is required
                if ($client->force_password_change) {
                    header('Location: change_password.php');
                } else {
                    header('Location: ../client/dashboard.php');
                }
                exit();
            } else {
                $error = 'Invalid credentials.';
            }
        }
    }
}

// Handle URL parameters
if (isset($_GET['error'])) {
    switch ($_GET['error']) {
        case 'unauthorized':
            $error = 'You are not authorized to access that page.';
            break;
        case 'session_expired':
            $error = 'Your session has expired. Please login again.';
            break;
    }
}

if (isset($_GET['success'])) {
    switch ($_GET['success']) {
        case 'password_changed':
            $success = 'Password changed successfully. Please login with your new password.';
            break;
        case 'logout':
            $success = 'You have been logged out successfully.';
            break;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Gas Cylinder Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
        }
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .btn-login:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        .user-type-tabs .nav-link {
            border-radius: 25px;
            margin: 0 5px;
        }
        .user-type-tabs .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: transparent;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="card login-card">
                    <div class="card-header login-header text-center py-4">
                        <h3><i class="fas fa-industry"></i> Sony Enterprises</h3>
                        <p class="mb-0">Gas Cylinder Management System</p>
                    </div>
                    <div class="card-body p-4">
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i> <?php echo $success; ?>
                            </div>
                        <?php endif; ?>
                        
                        <!-- User Type Tabs -->
                        <ul class="nav nav-pills user-type-tabs justify-content-center mb-4" id="userTypeTabs">
                            <li class="nav-item">
                                <a class="nav-link active" id="staff-tab" data-bs-toggle="pill" href="#staff" role="tab">
                                    <i class="fas fa-users"></i> Staff/Admin
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="client-tab" data-bs-toggle="pill" href="#client" role="tab">
                                    <i class="fas fa-user"></i> Client
                                </a>
                            </li>
                        </ul>
                        
                        <form method="POST" action="">
                            <input type="hidden" name="user_type" id="user_type" value="staff">
                            
                            <div class="mb-3">
                                <label for="username" class="form-label">
                                    <span id="username-label">Username</span>
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                    <input type="text" class="form-control" id="username" name="username" required>
                                </div>
                                <small class="form-text text-muted" id="username-help">
                                    Enter your username
                                </small>
                            </div>
                            
                            <div class="mb-4">
                                <label for="password" class="form-label">Password</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-login btn-primary w-100 py-2">
                                <i class="fas fa-sign-in-alt"></i> Login
                            </button>
                        </form>
                        
                        <div class="text-center mt-3">
                            <small class="text-muted">
                                Forgot password? Contact your administrator
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Toggle password visibility
        document.getElementById('togglePassword').addEventListener('click', function() {
            const password = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (password.type === 'password') {
                password.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                password.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
        
        // Handle user type tabs
        document.getElementById('staff-tab').addEventListener('click', function() {
            document.getElementById('user_type').value = 'staff';
            document.getElementById('username-label').textContent = 'Username';
            document.getElementById('username-help').textContent = 'Enter your username';
        });
        
        document.getElementById('client-tab').addEventListener('click', function() {
            document.getElementById('user_type').value = 'client';
            document.getElementById('username-label').textContent = 'Phone/Email/Client Code';
            document.getElementById('username-help').textContent = 'Enter your phone number, email, or client code';
        });
    </script>
</body>
</html>
