<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Redirect to login if not authenticated
if (!isset($_SESSION['user_id'])) {
    header('Location: auth/login.php');
    exit();
}

// Redirect to appropriate dashboard based on role
$role = $_SESSION['user_role'];
switch ($role) {
    case 'super_admin':
    case 'office_admin':
        header('Location: admin/dashboard.php');
        break;
    case 'refilling_staff':
        header('Location: staff/refilling_dashboard.php');
        break;
    case 'loading_staff':
        header('Location: staff/loading_dashboard.php');
        break;
    case 'accountant':
        header('Location: accounts/dashboard.php');
        break;
    case 'client':
        header('Location: client/dashboard.php');
        break;
    default:
        header('Location: auth/login.php');
        break;
}
exit();
?>
