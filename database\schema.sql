-- Gas Cylinder Management System Database Schema
-- Created for Sony Enterprises by Page Perfect Tech

CREATE DATABASE IF NOT EXISTS gas_cylinder_management;
USE gas_cylinder_management;

-- Company Settings Table
CREATE TABLE company_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_name VARCHAR(255) NOT NULL,
    logo VARCHAR(255),
    address TEXT,
    phone VA<PERSON><PERSON>R(20),
    email VARCHAR(100),
    gst_number VA<PERSON>HAR(20),
    pan_number VARCHAR(20),
    bank_name VARCHAR(100),
    bank_account VARCHAR(50),
    bank_ifsc VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Users Table (Staff and Admin)
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VA<PERSON>HA<PERSON>(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE,
    password VARCHAR(255) NOT NULL,
    full_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    phone VARCHAR(20),
    role ENUM('super_admin', 'office_admin', 'refilling_staff', 'loading_staff', 'accountant') NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    force_password_change BOOLEAN DEFAULT FALSE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Clients Table
CREATE TABLE clients (
    id INT PRIMARY KEY AUTO_INCREMENT,
    client_code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    company_name VARCHAR(100),
    phone VARCHAR(20) NOT NULL,
    email VARCHAR(100),
    address TEXT,
    gst_number VARCHAR(20),
    password VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    force_password_change BOOLEAN DEFAULT TRUE,
    special_pricing BOOLEAN DEFAULT FALSE,
    created_by INT,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Gas Types Table
CREATE TABLE gas_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,
    code VARCHAR(10) UNIQUE NOT NULL,
    description TEXT,
    unit ENUM('kg', 'liter', 'm3') DEFAULT 'kg',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Locations Table
CREATE TABLE locations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    address TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tanks Table
CREATE TABLE tanks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tank_code VARCHAR(20) UNIQUE NOT NULL,
    gas_type_id INT NOT NULL,
    location_id INT NOT NULL,
    capacity DECIMAL(10,2) NOT NULL,
    current_level DECIMAL(10,2) DEFAULT 0,
    unit ENUM('kg', 'liter', 'm3') DEFAULT 'kg',
    last_filled_date DATE,
    last_filled_quantity DECIMAL(10,2),
    wastage_threshold DECIMAL(5,2) DEFAULT 5.00,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (gas_type_id) REFERENCES gas_types(id),
    FOREIGN KEY (location_id) REFERENCES locations(id)
);

-- Cylinders Table
CREATE TABLE cylinders (
    id INT PRIMARY KEY AUTO_INCREMENT,
    cylinder_code VARCHAR(20) UNIQUE NOT NULL,
    barcode VARCHAR(50),
    qr_code TEXT,
    gas_type_id INT NOT NULL,
    size DECIMAL(8,2) NOT NULL,
    weight_empty DECIMAL(8,2),
    weight_full DECIMAL(8,2),
    pressure_rating DECIMAL(8,2),
    manufacture_date DATE,
    last_inspection_date DATE,
    expiry_date DATE,
    status ENUM('available', 'filled', 'dispatched', 'damaged', 'maintenance', 'lost') DEFAULT 'available',
    location_id INT,
    current_client_id INT NULL,
    damage_notes TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (gas_type_id) REFERENCES gas_types(id),
    FOREIGN KEY (location_id) REFERENCES locations(id),
    FOREIGN KEY (current_client_id) REFERENCES clients(id)
);

-- Orders Table
CREATE TABLE orders (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_number VARCHAR(20) UNIQUE NOT NULL,
    client_id INT NOT NULL,
    gas_type_id INT NOT NULL,
    quantity INT NOT NULL,
    unit_price DECIMAL(10,2),
    total_amount DECIMAL(10,2),
    status ENUM('pending', 'assigned', 'refilling', 'filled', 'loading', 'dispatched', 'delivered', 'cancelled') DEFAULT 'pending',
    assigned_to INT NULL,
    refill_completed_at TIMESTAMP NULL,
    loading_completed_at TIMESTAMP NULL,
    dispatched_at TIMESTAMP NULL,
    delivered_at TIMESTAMP NULL,
    notes TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id),
    FOREIGN KEY (gas_type_id) REFERENCES gas_types(id),
    FOREIGN KEY (assigned_to) REFERENCES users(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Order Cylinders Table (Many-to-many relationship)
CREATE TABLE order_cylinders (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_id INT NOT NULL,
    cylinder_id INT NOT NULL,
    filled_quantity DECIMAL(8,2),
    filled_at TIMESTAMP NULL,
    filled_by INT NULL,
    loaded_at TIMESTAMP NULL,
    loaded_by INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (cylinder_id) REFERENCES cylinders(id),
    FOREIGN KEY (filled_by) REFERENCES users(id),
    FOREIGN KEY (loaded_by) REFERENCES users(id),
    UNIQUE KEY unique_order_cylinder (order_id, cylinder_id)
);

-- Invoices Table
CREATE TABLE invoices (
    id INT PRIMARY KEY AUTO_INCREMENT,
    invoice_number VARCHAR(20) UNIQUE NOT NULL,
    client_id INT NOT NULL,
    invoice_date DATE NOT NULL,
    due_date DATE,
    subtotal DECIMAL(12,2) NOT NULL,
    tax_rate DECIMAL(5,2) DEFAULT 18.00,
    tax_amount DECIMAL(12,2),
    total_amount DECIMAL(12,2) NOT NULL,
    paid_amount DECIMAL(12,2) DEFAULT 0,
    balance_amount DECIMAL(12,2),
    status ENUM('draft', 'sent', 'paid', 'overdue', 'cancelled') DEFAULT 'draft',
    notes TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Invoice Items Table
CREATE TABLE invoice_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    invoice_id INT NOT NULL,
    order_id INT NOT NULL,
    description TEXT,
    quantity INT NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(12,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
    FOREIGN KEY (order_id) REFERENCES orders(id)
);

-- Payments Table
CREATE TABLE payments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    payment_number VARCHAR(20) UNIQUE NOT NULL,
    client_id INT NOT NULL,
    invoice_id INT NULL,
    amount DECIMAL(12,2) NOT NULL,
    payment_method ENUM('cash', 'upi', 'bank_transfer', 'cheque', 'card') NOT NULL,
    payment_date DATE NOT NULL,
    reference_number VARCHAR(50),
    bank_account_id INT NULL,
    notes TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id),
    FOREIGN KEY (invoice_id) REFERENCES invoices(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Bank Accounts Table
CREATE TABLE bank_accounts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    account_name VARCHAR(100) NOT NULL,
    bank_name VARCHAR(100) NOT NULL,
    account_number VARCHAR(50) NOT NULL,
    ifsc_code VARCHAR(20),
    account_type ENUM('savings', 'current', 'cash') DEFAULT 'current',
    opening_balance DECIMAL(12,2) DEFAULT 0,
    current_balance DECIMAL(12,2) DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Expenses Table
CREATE TABLE expenses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    expense_number VARCHAR(20) UNIQUE NOT NULL,
    category ENUM('salary', 'fuel', 'maintenance', 'utilities', 'rent', 'insurance', 'other') NOT NULL,
    description TEXT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    expense_date DATE NOT NULL,
    payment_method ENUM('cash', 'bank_transfer', 'cheque') NOT NULL,
    bank_account_id INT NULL,
    receipt_file VARCHAR(255),
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (bank_account_id) REFERENCES bank_accounts(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Tank Refill Logs Table
CREATE TABLE tank_refill_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tank_id INT NOT NULL,
    quantity_added DECIMAL(10,2) NOT NULL,
    previous_level DECIMAL(10,2),
    new_level DECIMAL(10,2),
    supplier VARCHAR(100),
    cost DECIMAL(10,2),
    refill_date DATE NOT NULL,
    notes TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tank_id) REFERENCES tanks(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Cylinder Movement Logs Table
CREATE TABLE cylinder_movement_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    cylinder_id INT NOT NULL,
    movement_type ENUM('inward', 'outward', 'transfer', 'maintenance', 'damage', 'lost') NOT NULL,
    from_location_id INT NULL,
    to_location_id INT NULL,
    client_id INT NULL,
    order_id INT NULL,
    quantity DECIMAL(8,2),
    notes TEXT,
    moved_by INT,
    movement_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (cylinder_id) REFERENCES cylinders(id),
    FOREIGN KEY (from_location_id) REFERENCES locations(id),
    FOREIGN KEY (to_location_id) REFERENCES locations(id),
    FOREIGN KEY (client_id) REFERENCES clients(id),
    FOREIGN KEY (order_id) REFERENCES orders(id),
    FOREIGN KEY (moved_by) REFERENCES users(id)
);

-- Wastage Logs Table
CREATE TABLE wastage_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tank_id INT NOT NULL,
    cylinder_id INT NULL,
    order_id INT NULL,
    expected_quantity DECIMAL(8,2) NOT NULL,
    actual_quantity DECIMAL(8,2) NOT NULL,
    wastage_quantity DECIMAL(8,2) NOT NULL,
    wastage_percentage DECIMAL(5,2) NOT NULL,
    reason TEXT,
    logged_date DATE NOT NULL,
    logged_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tank_id) REFERENCES tanks(id),
    FOREIGN KEY (cylinder_id) REFERENCES cylinders(id),
    FOREIGN KEY (order_id) REFERENCES orders(id),
    FOREIGN KEY (logged_by) REFERENCES users(id)
);

-- Notifications Table
CREATE TABLE notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    phone VARCHAR(20) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('order_placed', 'invoice_generated', 'payment_due', 'cylinder_return', 'general') DEFAULT 'general',
    status ENUM('pending', 'sent', 'failed') DEFAULT 'pending',
    sent_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Activity Logs Table
CREATE TABLE activity_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    action VARCHAR(100) NOT NULL,
    details TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Client Pricing Table
CREATE TABLE client_pricing (
    id INT PRIMARY KEY AUTO_INCREMENT,
    client_id INT NOT NULL,
    gas_type_id INT NOT NULL,
    special_price DECIMAL(10,2) NOT NULL,
    effective_from DATE NOT NULL,
    effective_to DATE NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id),
    FOREIGN KEY (gas_type_id) REFERENCES gas_types(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Create Indexes for better performance
CREATE INDEX idx_cylinders_status ON cylinders(status);
CREATE INDEX idx_cylinders_gas_type ON cylinders(gas_type_id);
CREATE INDEX idx_cylinders_location ON cylinders(location_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_client ON orders(client_id);
CREATE INDEX idx_orders_date ON orders(created_at);
CREATE INDEX idx_invoices_status ON invoices(status);
CREATE INDEX idx_invoices_client ON invoices(client_id);
CREATE INDEX idx_payments_date ON payments(payment_date);
CREATE INDEX idx_activity_logs_user ON activity_logs(user_id);
CREATE INDEX idx_activity_logs_date ON activity_logs(created_at);

-- Insert default company settings
INSERT INTO company_settings (company_name, address, phone, email) VALUES
('Sony Enterprises', 'Industrial Area, City', '+91-XXXXXXXXXX', '<EMAIL>');

-- Insert default super admin user
INSERT INTO users (username, email, password, full_name, phone, role, force_password_change) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System Administrator', '+91-XXXXXXXXXX', 'super_admin', TRUE);

-- Insert default gas types
INSERT INTO gas_types (name, code, description, unit) VALUES
('Oxygen', 'O2', 'Medical and Industrial Oxygen', 'kg'),
('Acetylene', 'C2H2', 'Welding Gas', 'kg'),
('Nitrogen', 'N2', 'Industrial Nitrogen', 'kg'),
('Carbon Dioxide', 'CO2', 'Industrial CO2', 'kg'),
('Argon', 'AR', 'Welding and Industrial Argon', 'kg'),
('LPG', 'LPG', 'Liquefied Petroleum Gas', 'kg');

-- Insert default location
INSERT INTO locations (name, address) VALUES
('Main Warehouse', 'Sony Enterprises Main Facility'),
('Branch Office', 'Sony Enterprises Branch Location');

-- Insert default bank account
INSERT INTO bank_accounts (account_name, bank_name, account_number, ifsc_code, account_type, opening_balance, current_balance) VALUES
('Sony Enterprises', 'State Bank of India', '**********', 'SBIN0001234', 'current', 0.00, 0.00);
