<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Check if user is logged in and has admin role
requireRole(['super_admin', 'office_admin']);

$errors = [];
$success = '';

// Get gas types and locations
$db->query("SELECT * FROM gas_types WHERE is_active = 1 ORDER BY name");
$gas_types = $db->resultset();

$db->query("SELECT * FROM locations WHERE is_active = 1 ORDER BY name");
$locations = $db->resultset();

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $gas_type_id = (int)$_POST['gas_type_id'];
    $size = (float)$_POST['size'];
    $weight_empty = !empty($_POST['weight_empty']) ? (float)$_POST['weight_empty'] : null;
    $weight_full = !empty($_POST['weight_full']) ? (float)$_POST['weight_full'] : null;
    $pressure_rating = !empty($_POST['pressure_rating']) ? (float)$_POST['pressure_rating'] : null;
    $manufacture_date = !empty($_POST['manufacture_date']) ? $_POST['manufacture_date'] : null;
    $last_inspection_date = !empty($_POST['last_inspection_date']) ? $_POST['last_inspection_date'] : null;
    $expiry_date = !empty($_POST['expiry_date']) ? $_POST['expiry_date'] : null;
    $location_id = !empty($_POST['location_id']) ? (int)$_POST['location_id'] : null;
    $barcode = sanitize($_POST['barcode']);
    $auto_generate_code = isset($_POST['auto_generate_code']) ? 1 : 0;
    $cylinder_code = $auto_generate_code ? '' : sanitize($_POST['cylinder_code']);
    $generate_qr = isset($_POST['generate_qr']) ? 1 : 0;
    
    // Validation
    if (empty($gas_type_id)) {
        $errors[] = 'Please select a gas type.';
    }
    
    if (empty($size) || $size <= 0) {
        $errors[] = 'Please enter a valid cylinder size.';
    }
    
    if (!$auto_generate_code && empty($cylinder_code)) {
        $errors[] = 'Please enter a cylinder code or enable auto-generation.';
    }
    
    // Generate cylinder code if auto-generation is enabled
    if ($auto_generate_code && empty($errors)) {
        // Get gas type for code generation
        $db->query("SELECT name FROM gas_types WHERE id = :id");
        $db->bind(':id', $gas_type_id);
        $gas_type = $db->single();
        
        if ($gas_type) {
            $cylinder_code = generateCylinderCode($gas_type->name, $size);
        } else {
            $errors[] = 'Invalid gas type selected.';
        }
    }
    
    // Check if cylinder code already exists
    if (!empty($cylinder_code) && empty($errors)) {
        $db->query("SELECT id FROM cylinders WHERE cylinder_code = :cylinder_code");
        $db->bind(':cylinder_code', $cylinder_code);
        if ($db->single()) {
            $errors[] = 'Cylinder code already exists.';
        }
    }
    
    // Check if barcode already exists (if provided)
    if (!empty($barcode) && empty($errors)) {
        $db->query("SELECT id FROM cylinders WHERE barcode = :barcode");
        $db->bind(':barcode', $barcode);
        if ($db->single()) {
            $errors[] = 'Barcode already exists.';
        }
    }
    
    // Validate dates
    if (!empty($manufacture_date) && !empty($expiry_date)) {
        if (strtotime($expiry_date) <= strtotime($manufacture_date)) {
            $errors[] = 'Expiry date must be after manufacture date.';
        }
    }
    
    if (!empty($manufacture_date) && !empty($last_inspection_date)) {
        if (strtotime($last_inspection_date) < strtotime($manufacture_date)) {
            $errors[] = 'Last inspection date cannot be before manufacture date.';
        }
    }
    
    if (empty($errors)) {
        try {
            // Generate QR code data if requested
            $qr_code = '';
            if ($generate_qr) {
                $qr_data = [
                    'cylinder_code' => $cylinder_code,
                    'gas_type_id' => $gas_type_id,
                    'size' => $size
                ];
                $qr_code = generateQRData('cylinder', 0, $qr_data);
            }
            
            // Insert cylinder
            $db->query("INSERT INTO cylinders (
                cylinder_code, barcode, qr_code, gas_type_id, size, weight_empty, weight_full, 
                pressure_rating, manufacture_date, last_inspection_date, expiry_date, 
                location_id, status, created_at
            ) VALUES (
                :cylinder_code, :barcode, :qr_code, :gas_type_id, :size, :weight_empty, :weight_full, 
                :pressure_rating, :manufacture_date, :last_inspection_date, :expiry_date, 
                :location_id, 'available', NOW()
            )");
            
            $db->bind(':cylinder_code', $cylinder_code);
            $db->bind(':barcode', $barcode);
            $db->bind(':qr_code', $qr_code);
            $db->bind(':gas_type_id', $gas_type_id);
            $db->bind(':size', $size);
            $db->bind(':weight_empty', $weight_empty);
            $db->bind(':weight_full', $weight_full);
            $db->bind(':pressure_rating', $pressure_rating);
            $db->bind(':manufacture_date', $manufacture_date);
            $db->bind(':last_inspection_date', $last_inspection_date);
            $db->bind(':expiry_date', $expiry_date);
            $db->bind(':location_id', $location_id);
            
            $db->execute();
            $cylinder_id = $db->lastInsertId();
            
            // Update QR code with actual cylinder ID
            if ($generate_qr) {
                $qr_data['id'] = $cylinder_id;
                $qr_code = generateQRData('cylinder', $cylinder_id, $qr_data);
                
                $db->query("UPDATE cylinders SET qr_code = :qr_code WHERE id = :id");
                $db->bind(':qr_code', $qr_code);
                $db->bind(':id', $cylinder_id);
                $db->execute();
            }
            
            // Log activity
            logActivity($_SESSION['user_id'], 'cylinder_created', "Created cylinder: $cylinder_code");
            
            setFlashMessage('success', "Cylinder created successfully! Cylinder Code: $cylinder_code");
            header('Location: view.php?id=' . $cylinder_id);
            exit();
            
        } catch (Exception $e) {
            $errors[] = 'Error creating cylinder: ' . $e->getMessage();
        }
    }
}

$page_title = 'Add New Cylinder';
include '../../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../../includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-plus"></i> Add New Cylinder</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Cylinders
                    </a>
                </div>
            </div>

            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Cylinder Information</h6>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="">
                                <!-- Basic Information -->
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="gas_type_id" class="form-label">Gas Type <span class="text-danger">*</span></label>
                                        <select class="form-select" id="gas_type_id" name="gas_type_id" required>
                                            <option value="">Select Gas Type</option>
                                            <?php foreach ($gas_types as $gas_type): ?>
                                                <option value="<?php echo $gas_type->id; ?>" 
                                                        <?php echo (isset($_POST['gas_type_id']) && $_POST['gas_type_id'] == $gas_type->id) ? 'selected' : ''; ?>>
                                                    <?php echo $gas_type->name; ?> (<?php echo $gas_type->code; ?>)
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="size" class="form-label">Size (kg) <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control" id="size" name="size" 
                                               value="<?php echo isset($_POST['size']) ? $_POST['size'] : ''; ?>" 
                                               step="0.1" min="0" required>
                                    </div>
                                </div>

                                <!-- Cylinder Code -->
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="auto_generate_code" name="auto_generate_code" 
                                                   <?php echo isset($_POST['auto_generate_code']) ? 'checked' : 'checked'; ?>>
                                            <label class="form-check-label" for="auto_generate_code">
                                                Auto-generate Cylinder Code
                                            </label>
                                        </div>
                                        <label for="cylinder_code" class="form-label">Cylinder Code</label>
                                        <input type="text" class="form-control" id="cylinder_code" name="cylinder_code" 
                                               value="<?php echo isset($_POST['cylinder_code']) ? htmlspecialchars($_POST['cylinder_code']) : ''; ?>"
                                               placeholder="Will be auto-generated">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="barcode" class="form-label">Barcode (Optional)</label>
                                        <input type="text" class="form-control" id="barcode" name="barcode" 
                                               value="<?php echo isset($_POST['barcode']) ? htmlspecialchars($_POST['barcode']) : ''; ?>"
                                               placeholder="Existing barcode if any">
                                    </div>
                                </div>

                                <!-- Physical Properties -->
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="weight_empty" class="form-label">Empty Weight (kg)</label>
                                        <input type="number" class="form-control" id="weight_empty" name="weight_empty" 
                                               value="<?php echo isset($_POST['weight_empty']) ? $_POST['weight_empty'] : ''; ?>" 
                                               step="0.1" min="0">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="weight_full" class="form-label">Full Weight (kg)</label>
                                        <input type="number" class="form-control" id="weight_full" name="weight_full" 
                                               value="<?php echo isset($_POST['weight_full']) ? $_POST['weight_full'] : ''; ?>" 
                                               step="0.1" min="0">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="pressure_rating" class="form-label">Pressure Rating (bar)</label>
                                        <input type="number" class="form-control" id="pressure_rating" name="pressure_rating" 
                                               value="<?php echo isset($_POST['pressure_rating']) ? $_POST['pressure_rating'] : ''; ?>" 
                                               step="0.1" min="0">
                                    </div>
                                </div>

                                <!-- Dates -->
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="manufacture_date" class="form-label">Manufacture Date</label>
                                        <input type="date" class="form-control" id="manufacture_date" name="manufacture_date" 
                                               value="<?php echo isset($_POST['manufacture_date']) ? $_POST['manufacture_date'] : ''; ?>">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="last_inspection_date" class="form-label">Last Inspection Date</label>
                                        <input type="date" class="form-control" id="last_inspection_date" name="last_inspection_date" 
                                               value="<?php echo isset($_POST['last_inspection_date']) ? $_POST['last_inspection_date'] : ''; ?>">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="expiry_date" class="form-label">Expiry Date</label>
                                        <input type="date" class="form-control" id="expiry_date" name="expiry_date" 
                                               value="<?php echo isset($_POST['expiry_date']) ? $_POST['expiry_date'] : ''; ?>">
                                    </div>
                                </div>

                                <!-- Location -->
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="location_id" class="form-label">Initial Location</label>
                                        <select class="form-select" id="location_id" name="location_id">
                                            <option value="">Select Location</option>
                                            <?php foreach ($locations as $location): ?>
                                                <option value="<?php echo $location->id; ?>" 
                                                        <?php echo (isset($_POST['location_id']) && $_POST['location_id'] == $location->id) ? 'selected' : ''; ?>>
                                                    <?php echo $location->name; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="form-check mt-4">
                                            <input class="form-check-input" type="checkbox" id="generate_qr" name="generate_qr" checked>
                                            <label class="form-check-label" for="generate_qr">
                                                Generate QR Code
                                            </label>
                                            <div class="form-text">QR code will contain cylinder information for scanning</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="index.php" class="btn btn-secondary me-md-2">Cancel</a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> Create Cylinder
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Important Notes</h6>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> Cylinder Code</h6>
                                <p class="mb-0">Auto-generated codes follow the format: [GAS][SIZE][RANDOM] (e.g., OX051234 for Oxygen 5kg cylinder)</p>
                            </div>

                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle"></i> Inspection Schedule</h6>
                                <p class="mb-0">Cylinders typically need inspection every 5 years. Set appropriate dates to track inspection due dates.</p>
                            </div>

                            <div class="alert alert-success">
                                <h6><i class="fas fa-qrcode"></i> QR Code</h6>
                                <p class="mb-0">QR codes enable quick scanning and tracking. They contain cylinder information and can be printed as labels.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Stats -->
                    <div class="card shadow mt-3">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Current Inventory</h6>
                        </div>
                        <div class="card-body">
                            <?php
                            $db->query("SELECT g.name, COUNT(*) as count 
                                       FROM cylinders c 
                                       JOIN gas_types g ON c.gas_type_id = g.id 
                                       WHERE c.is_active = 1 
                                       GROUP BY g.id, g.name 
                                       ORDER BY count DESC");
                            $inventory = $db->resultset();
                            ?>
                            
                            <?php if (!empty($inventory)): ?>
                                <?php foreach ($inventory as $item): ?>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span><?php echo $item->name; ?></span>
                                    <span class="badge bg-primary"><?php echo $item->count; ?></span>
                                </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <p class="text-muted mb-0">No cylinders in inventory yet.</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
// Toggle cylinder code input based on auto-generation checkbox
document.getElementById('auto_generate_code').addEventListener('change', function() {
    const cylinderCodeInput = document.getElementById('cylinder_code');
    if (this.checked) {
        cylinderCodeInput.disabled = true;
        cylinderCodeInput.placeholder = 'Will be auto-generated';
        cylinderCodeInput.value = '';
    } else {
        cylinderCodeInput.disabled = false;
        cylinderCodeInput.placeholder = 'Enter cylinder code';
    }
});

// Initialize the state
document.getElementById('auto_generate_code').dispatchEvent(new Event('change'));

// Set max date for manufacture date to today
document.getElementById('manufacture_date').max = new Date().toISOString().split('T')[0];

// Set min date for inspection and expiry based on manufacture date
document.getElementById('manufacture_date').addEventListener('change', function() {
    const inspectionDate = document.getElementById('last_inspection_date');
    const expiryDate = document.getElementById('expiry_date');
    
    if (this.value) {
        inspectionDate.min = this.value;
        expiryDate.min = this.value;
    }
});
</script>

<?php include '../../includes/footer.php'; ?>
