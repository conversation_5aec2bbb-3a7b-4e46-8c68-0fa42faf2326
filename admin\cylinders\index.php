<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Check if user is logged in and has admin role
requireRole(['super_admin', 'office_admin']);

// Handle search and filters
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
$status_filter = isset($_GET['status']) ? sanitize($_GET['status']) : '';
$gas_type_filter = isset($_GET['gas_type']) ? (int)$_GET['gas_type'] : 0;
$location_filter = isset($_GET['location']) ? (int)$_GET['location'] : 0;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$records_per_page = 25;

// Build query
$where_conditions = ['c.is_active = 1'];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(c.cylinder_code LIKE :search OR c.barcode LIKE :search)";
    $params[':search'] = "%$search%";
}

if (!empty($status_filter)) {
    $where_conditions[] = "c.status = :status";
    $params[':status'] = $status_filter;
}

if (!empty($gas_type_filter)) {
    $where_conditions[] = "c.gas_type_id = :gas_type";
    $params[':gas_type'] = $gas_type_filter;
}

if (!empty($location_filter)) {
    $where_conditions[] = "c.location_id = :location";
    $params[':location'] = $location_filter;
}

$where_clause = implode(' AND ', $where_conditions);

// Get total count for pagination
$count_query = "SELECT COUNT(*) as total FROM cylinders c WHERE $where_clause";
$db->query($count_query);
foreach ($params as $key => $value) {
    $db->bind($key, $value);
}
$total_records = $db->single()->total;

// Calculate pagination
$pagination = paginate($total_records, $records_per_page, $page);

// Get cylinders
$query = "SELECT c.*, g.name as gas_name, g.code as gas_code, l.name as location_name, 
          cl.name as client_name, cl.client_code
          FROM cylinders c 
          JOIN gas_types g ON c.gas_type_id = g.id 
          LEFT JOIN locations l ON c.location_id = l.id 
          LEFT JOIN clients cl ON c.current_client_id = cl.id 
          WHERE $where_clause 
          ORDER BY c.created_at DESC 
          LIMIT {$pagination['limit']} OFFSET {$pagination['offset']}";

$db->query($query);
foreach ($params as $key => $value) {
    $db->bind($key, $value);
}
$cylinders = $db->resultset();

// Get filter options
$db->query("SELECT * FROM gas_types WHERE is_active = 1 ORDER BY name");
$gas_types = $db->resultset();

$db->query("SELECT * FROM locations WHERE is_active = 1 ORDER BY name");
$locations = $db->resultset();

// Get statistics
$db->query("SELECT 
    COUNT(*) as total,
    SUM(CASE WHEN status = 'available' THEN 1 ELSE 0 END) as available,
    SUM(CASE WHEN status = 'filled' THEN 1 ELSE 0 END) as filled,
    SUM(CASE WHEN status = 'dispatched' THEN 1 ELSE 0 END) as dispatched,
    SUM(CASE WHEN status = 'damaged' THEN 1 ELSE 0 END) as damaged
    FROM cylinders WHERE is_active = 1");
$stats = $db->single();

$page_title = 'Cylinder Management';
include '../../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../../includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-fire-extinguisher"></i> Cylinder Management</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="create.php" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i> Add New Cylinder
                        </a>
                        <a href="scanner.php" class="btn btn-sm btn-info">
                            <i class="fas fa-qrcode"></i> QR Scanner
                        </a>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportToCSV('cylindersTable', 'cylinders')">
                            <i class="fas fa-download"></i> Export CSV
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="printTable('cylindersTable')">
                            <i class="fas fa-print"></i> Print
                        </button>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-2">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats->total; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-fire-extinguisher fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Available</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats->available; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Filled</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats->filled; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-fill-drip fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Dispatched</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats->dispatched; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-truck fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card border-left-danger shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Damaged</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats->damaged; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo htmlspecialchars($search); ?>" 
                                   placeholder="Cylinder code or barcode">
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">All Status</option>
                                <option value="available" <?php echo $status_filter === 'available' ? 'selected' : ''; ?>>Available</option>
                                <option value="filled" <?php echo $status_filter === 'filled' ? 'selected' : ''; ?>>Filled</option>
                                <option value="dispatched" <?php echo $status_filter === 'dispatched' ? 'selected' : ''; ?>>Dispatched</option>
                                <option value="damaged" <?php echo $status_filter === 'damaged' ? 'selected' : ''; ?>>Damaged</option>
                                <option value="maintenance" <?php echo $status_filter === 'maintenance' ? 'selected' : ''; ?>>Maintenance</option>
                                <option value="lost" <?php echo $status_filter === 'lost' ? 'selected' : ''; ?>>Lost</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="gas_type" class="form-label">Gas Type</label>
                            <select class="form-select" id="gas_type" name="gas_type">
                                <option value="">All Gas Types</option>
                                <?php foreach ($gas_types as $gas_type): ?>
                                    <option value="<?php echo $gas_type->id; ?>" <?php echo $gas_type_filter == $gas_type->id ? 'selected' : ''; ?>>
                                        <?php echo $gas_type->name; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="location" class="form-label">Location</label>
                            <select class="form-select" id="location" name="location">
                                <option value="">All Locations</option>
                                <?php foreach ($locations as $location): ?>
                                    <option value="<?php echo $location->id; ?>" <?php echo $location_filter == $location->id ? 'selected' : ''; ?>>
                                        <?php echo $location->name; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search"></i> Search
                            </button>
                            <a href="index.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> Clear
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Cylinders Table -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        Cylinders List (<?php echo $total_records; ?> total)
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="cylindersTable">
                            <thead>
                                <tr>
                                    <th>Cylinder Code</th>
                                    <th>Gas Type</th>
                                    <th>Size</th>
                                    <th>Status</th>
                                    <th>Location</th>
                                    <th>Current Client</th>
                                    <th>Last Inspection</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($cylinders as $cylinder): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo $cylinder->cylinder_code; ?></strong>
                                        <?php if (!empty($cylinder->barcode)): ?>
                                            <br><small class="text-muted">BC: <?php echo $cylinder->barcode; ?></small>
                                        <?php endif; ?>
                                        <?php if (!empty($cylinder->qr_code)): ?>
                                            <br><span class="badge bg-info">QR</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary"><?php echo $cylinder->gas_code; ?></span>
                                        <br><?php echo $cylinder->gas_name; ?>
                                    </td>
                                    <td><?php echo $cylinder->size; ?> kg</td>
                                    <td>
                                        <span class="badge bg-<?php 
                                            echo $cylinder->status == 'available' ? 'success' : 
                                                ($cylinder->status == 'filled' ? 'info' : 
                                                ($cylinder->status == 'dispatched' ? 'primary' : 
                                                ($cylinder->status == 'damaged' ? 'danger' : 'warning'))); 
                                        ?>">
                                            <?php echo ucfirst($cylinder->status); ?>
                                        </span>
                                    </td>
                                    <td><?php echo htmlspecialchars($cylinder->location_name ?? '-'); ?></td>
                                    <td>
                                        <?php if ($cylinder->client_name): ?>
                                            <strong><?php echo htmlspecialchars($cylinder->client_name); ?></strong>
                                            <br><small class="text-muted"><?php echo $cylinder->client_code; ?></small>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($cylinder->last_inspection_date): ?>
                                            <?php echo formatDate($cylinder->last_inspection_date); ?>
                                            <?php if (needsInspection($cylinder->last_inspection_date)): ?>
                                                <br><span class="badge bg-warning">Due</span>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="badge bg-danger">Never</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="view.php?id=<?php echo $cylinder->id; ?>" class="btn btn-sm btn-info" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="edit.php?id=<?php echo $cylinder->id; ?>" class="btn btn-sm btn-warning" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="qr.php?id=<?php echo $cylinder->id; ?>" class="btn btn-sm btn-secondary" title="Generate QR">
                                                <i class="fas fa-qrcode"></i>
                                            </a>
                                            <?php if (hasRole('super_admin')): ?>
                                            <a href="delete.php?id=<?php echo $cylinder->id; ?>" 
                                               class="btn btn-sm btn-danger delete-btn" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if ($pagination['total_pages'] > 1): ?>
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            <?php if ($pagination['has_previous']): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $pagination['current_page'] - 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status_filter); ?>&gas_type=<?php echo $gas_type_filter; ?>&location=<?php echo $location_filter; ?>">Previous</a>
                                </li>
                            <?php endif; ?>
                            
                            <?php for ($i = 1; $i <= $pagination['total_pages']; $i++): ?>
                                <li class="page-item <?php echo $i == $pagination['current_page'] ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status_filter); ?>&gas_type=<?php echo $gas_type_filter; ?>&location=<?php echo $location_filter; ?>"><?php echo $i; ?></a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($pagination['has_next']): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $pagination['current_page'] + 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status_filter); ?>&gas_type=<?php echo $gas_type_filter; ?>&location=<?php echo $location_filter; ?>">Next</a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include '../../includes/footer.php'; ?>
