<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Check if user is logged in and has admin role
requireRole(['super_admin', 'office_admin']);

$client_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$client_id) {
    setFlashMessage('error', 'Invalid client ID.');
    header('Location: index.php');
    exit();
}

// Get client details
$db->query("SELECT c.*, u.full_name as created_by_name 
           FROM clients c 
           LEFT JOIN users u ON c.created_by = u.id 
           WHERE c.id = :id");
$db->bind(':id', $client_id);
$client = $db->single();

if (!$client) {
    setFlashMessage('error', 'Client not found.');
    header('Location: index.php');
    exit();
}

// Get client statistics
$stats = getClientStats($client_id);

// Get recent orders
$db->query("SELECT o.*, g.name as gas_name 
           FROM orders o 
           JOIN gas_types g ON o.gas_type_id = g.id 
           WHERE o.client_id = :client_id 
           ORDER BY o.created_at DESC LIMIT 10");
$db->bind(':client_id', $client_id);
$recent_orders = $db->resultset();

// Get recent invoices
$db->query("SELECT * FROM invoices 
           WHERE client_id = :client_id 
           ORDER BY created_at DESC LIMIT 10");
$db->bind(':client_id', $client_id);
$recent_invoices = $db->resultset();

// Get special pricing
$db->query("SELECT cp.*, g.name as gas_name 
           FROM client_pricing cp 
           JOIN gas_types g ON cp.gas_type_id = g.id 
           WHERE cp.client_id = :client_id AND cp.is_active = 1 
           ORDER BY g.name");
$db->bind(':client_id', $client_id);
$special_pricing = $db->resultset();

// Get cylinders with client
$db->query("SELECT c.*, g.name as gas_name 
           FROM cylinders c 
           JOIN gas_types g ON c.gas_type_id = g.id 
           WHERE c.current_client_id = :client_id 
           ORDER BY c.created_at DESC");
$db->bind(':client_id', $client_id);
$client_cylinders = $db->resultset();

$page_title = 'Client Details - ' . $client->name;
include '../../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../../includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-user"></i> Client Details</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="edit.php?id=<?php echo $client->id; ?>" class="btn btn-sm btn-warning">
                            <i class="fas fa-edit"></i> Edit Client
                        </a>
                        <a href="pricing.php?id=<?php echo $client->id; ?>" class="btn btn-sm btn-info">
                            <i class="fas fa-tags"></i> Special Pricing
                        </a>
                        <a href="<?php echo generateWhatsAppURL($client->phone, 'Hello ' . $client->name . ', this is Sony Enterprises.'); ?>" 
                           target="_blank" class="btn btn-sm btn-success">
                            <i class="fab fa-whatsapp"></i> WhatsApp
                        </a>
                    </div>
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Clients
                    </a>
                </div>
            </div>

            <!-- Client Information Card -->
            <div class="row mb-4">
                <div class="col-lg-8">
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Client Information</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Client Code:</strong></td>
                                            <td><?php echo $client->client_code; ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Name:</strong></td>
                                            <td><?php echo htmlspecialchars($client->name); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Company:</strong></td>
                                            <td><?php echo htmlspecialchars($client->company_name ?? '-'); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Phone:</strong></td>
                                            <td><?php echo $client->phone; ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Email:</strong></td>
                                            <td><?php echo htmlspecialchars($client->email ?? '-'); ?></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>GST Number:</strong></td>
                                            <td><?php echo htmlspecialchars($client->gst_number ?? '-'); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Status:</strong></td>
                                            <td>
                                                <?php if ($client->is_active): ?>
                                                    <span class="badge bg-success">Active</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">Inactive</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Special Pricing:</strong></td>
                                            <td>
                                                <?php if ($client->special_pricing): ?>
                                                    <span class="badge bg-info">Enabled</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">Disabled</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Created By:</strong></td>
                                            <td><?php echo htmlspecialchars($client->created_by_name ?? '-'); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Created Date:</strong></td>
                                            <td><?php echo formatDateTime($client->created_at); ?></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            
                            <?php if (!empty($client->address)): ?>
                            <div class="row">
                                <div class="col-12">
                                    <strong>Address:</strong><br>
                                    <?php echo nl2br(htmlspecialchars($client->address)); ?>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Statistics Card -->
                <div class="col-lg-4">
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Statistics</h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6 mb-3">
                                    <div class="border-bottom pb-2">
                                        <h4 class="text-primary"><?php echo $stats['total_orders']; ?></h4>
                                        <small>Total Orders</small>
                                    </div>
                                </div>
                                <div class="col-6 mb-3">
                                    <div class="border-bottom pb-2">
                                        <h4 class="text-warning"><?php echo $stats['pending_orders']; ?></h4>
                                        <small>Pending Orders</small>
                                    </div>
                                </div>
                                <div class="col-6 mb-3">
                                    <div class="border-bottom pb-2">
                                        <h4 class="text-info"><?php echo $stats['total_invoices']; ?></h4>
                                        <small>Total Invoices</small>
                                    </div>
                                </div>
                                <div class="col-6 mb-3">
                                    <div class="border-bottom pb-2">
                                        <h4 class="text-success"><?php echo $stats['cylinders_with_client']; ?></h4>
                                        <small>Cylinders</small>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <h5 class="<?php echo $stats['outstanding_amount'] > 0 ? 'text-danger' : 'text-success'; ?>">
                                        <?php echo formatCurrency($stats['outstanding_amount']); ?>
                                    </h5>
                                    <small>Outstanding Amount</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tabs for detailed information -->
            <div class="card shadow">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" id="clientTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="orders-tab" data-bs-toggle="tab" data-bs-target="#orders" type="button" role="tab">
                                <i class="fas fa-clipboard-list"></i> Recent Orders
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="invoices-tab" data-bs-toggle="tab" data-bs-target="#invoices" type="button" role="tab">
                                <i class="fas fa-file-invoice"></i> Recent Invoices
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="cylinders-tab" data-bs-toggle="tab" data-bs-target="#cylinders" type="button" role="tab">
                                <i class="fas fa-fire-extinguisher"></i> Cylinders
                            </button>
                        </li>
                        <?php if (!empty($special_pricing)): ?>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="pricing-tab" data-bs-toggle="tab" data-bs-target="#pricing" type="button" role="tab">
                                <i class="fas fa-tags"></i> Special Pricing
                            </button>
                        </li>
                        <?php endif; ?>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="clientTabsContent">
                        <!-- Orders Tab -->
                        <div class="tab-pane fade show active" id="orders" role="tabpanel">
                            <?php if (!empty($recent_orders)): ?>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Order #</th>
                                            <th>Gas Type</th>
                                            <th>Quantity</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                            <th>Date</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_orders as $order): ?>
                                        <tr>
                                            <td><?php echo $order->order_number; ?></td>
                                            <td><?php echo $order->gas_name; ?></td>
                                            <td><?php echo $order->quantity; ?></td>
                                            <td><?php echo formatCurrency($order->total_amount); ?></td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo $order->status == 'pending' ? 'warning' : 
                                                        ($order->status == 'dispatched' ? 'success' : 'info'); 
                                                ?>">
                                                    <?php echo ucfirst($order->status); ?>
                                                </span>
                                            </td>
                                            <td><?php echo formatDate($order->created_at); ?></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-center">
                                <a href="../orders/index.php?client_id=<?php echo $client->id; ?>" class="btn btn-sm btn-primary">
                                    View All Orders
                                </a>
                            </div>
                            <?php else: ?>
                            <div class="text-center text-muted">
                                <i class="fas fa-clipboard-list fa-3x mb-3"></i>
                                <p>No orders found for this client.</p>
                                <a href="../orders/create.php?client_id=<?php echo $client->id; ?>" class="btn btn-primary">
                                    Create First Order
                                </a>
                            </div>
                            <?php endif; ?>
                        </div>

                        <!-- Invoices Tab -->
                        <div class="tab-pane fade" id="invoices" role="tabpanel">
                            <?php if (!empty($recent_invoices)): ?>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Invoice #</th>
                                            <th>Date</th>
                                            <th>Total Amount</th>
                                            <th>Paid Amount</th>
                                            <th>Balance</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_invoices as $invoice): ?>
                                        <tr>
                                            <td><?php echo $invoice->invoice_number; ?></td>
                                            <td><?php echo formatDate($invoice->invoice_date); ?></td>
                                            <td><?php echo formatCurrency($invoice->total_amount); ?></td>
                                            <td><?php echo formatCurrency($invoice->paid_amount); ?></td>
                                            <td><?php echo formatCurrency($invoice->balance_amount); ?></td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo $invoice->status == 'paid' ? 'success' : 
                                                        ($invoice->status == 'overdue' ? 'danger' : 'warning'); 
                                                ?>">
                                                    <?php echo ucfirst($invoice->status); ?>
                                                </span>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-center">
                                <a href="../invoices/index.php?client_id=<?php echo $client->id; ?>" class="btn btn-sm btn-primary">
                                    View All Invoices
                                </a>
                            </div>
                            <?php else: ?>
                            <div class="text-center text-muted">
                                <i class="fas fa-file-invoice fa-3x mb-3"></i>
                                <p>No invoices found for this client.</p>
                            </div>
                            <?php endif; ?>
                        </div>

                        <!-- Cylinders Tab -->
                        <div class="tab-pane fade" id="cylinders" role="tabpanel">
                            <?php if (!empty($client_cylinders)): ?>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Cylinder Code</th>
                                            <th>Gas Type</th>
                                            <th>Size</th>
                                            <th>Status</th>
                                            <th>Since</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($client_cylinders as $cylinder): ?>
                                        <tr>
                                            <td><?php echo $cylinder->cylinder_code; ?></td>
                                            <td><?php echo $cylinder->gas_name; ?></td>
                                            <td><?php echo $cylinder->size; ?> kg</td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo $cylinder->status == 'filled' ? 'success' : 
                                                        ($cylinder->status == 'damaged' ? 'danger' : 'info'); 
                                                ?>">
                                                    <?php echo ucfirst($cylinder->status); ?>
                                                </span>
                                            </td>
                                            <td><?php echo formatDate($cylinder->updated_at); ?></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php else: ?>
                            <div class="text-center text-muted">
                                <i class="fas fa-fire-extinguisher fa-3x mb-3"></i>
                                <p>No cylinders currently with this client.</p>
                            </div>
                            <?php endif; ?>
                        </div>

                        <!-- Special Pricing Tab -->
                        <?php if (!empty($special_pricing)): ?>
                        <div class="tab-pane fade" id="pricing" role="tabpanel">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Gas Type</th>
                                            <th>Special Price</th>
                                            <th>Effective From</th>
                                            <th>Effective To</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($special_pricing as $pricing): ?>
                                        <tr>
                                            <td><?php echo $pricing->gas_name; ?></td>
                                            <td><?php echo formatCurrency($pricing->special_price); ?></td>
                                            <td><?php echo formatDate($pricing->effective_from); ?></td>
                                            <td><?php echo $pricing->effective_to ? formatDate($pricing->effective_to) : 'Ongoing'; ?></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-center">
                                <a href="pricing.php?id=<?php echo $client->id; ?>" class="btn btn-sm btn-primary">
                                    Manage Pricing
                                </a>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include '../../includes/footer.php'; ?>
