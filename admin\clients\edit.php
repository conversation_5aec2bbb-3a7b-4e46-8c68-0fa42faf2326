<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Check if user is logged in and has admin role
requireRole(['super_admin', 'office_admin']);

$client_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$errors = [];
$success = '';

if (!$client_id) {
    setFlashMessage('error', 'Invalid client ID.');
    header('Location: index.php');
    exit();
}

// Get client details
$db->query("SELECT * FROM clients WHERE id = :id");
$db->bind(':id', $client_id);
$client = $db->single();

if (!$client) {
    setFlashMessage('error', 'Client not found.');
    header('Location: index.php');
    exit();
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $name = sanitize($_POST['name']);
    $company_name = sanitize($_POST['company_name']);
    $phone = sanitize($_POST['phone']);
    $email = sanitize($_POST['email']);
    $address = sanitize($_POST['address']);
    $gst_number = sanitize($_POST['gst_number']);
    $special_pricing = isset($_POST['special_pricing']) ? 1 : 0;
    $is_active = isset($_POST['is_active']) ? 1 : 0;
    $reset_password = isset($_POST['reset_password']) ? 1 : 0;
    
    // Validation
    if (empty($name)) {
        $errors[] = 'Client name is required.';
    }
    
    if (empty($phone)) {
        $errors[] = 'Phone number is required.';
    } elseif (!preg_match('/^[0-9]{10}$/', $phone)) {
        $errors[] = 'Phone number must be 10 digits.';
    }
    
    if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Invalid email format.';
    }
    
    // Check if phone already exists (excluding current client)
    if (empty($errors)) {
        $db->query("SELECT id FROM clients WHERE phone = :phone AND id != :id");
        $db->bind(':phone', $phone);
        $db->bind(':id', $client_id);
        if ($db->single()) {
            $errors[] = 'Phone number already exists.';
        }
    }
    
    // Check if email already exists (excluding current client)
    if (empty($errors) && !empty($email)) {
        $db->query("SELECT id FROM clients WHERE email = :email AND id != :id");
        $db->bind(':email', $email);
        $db->bind(':id', $client_id);
        if ($db->single()) {
            $errors[] = 'Email already exists.';
        }
    }
    
    if (empty($errors)) {
        try {
            // Update client
            $db->query("UPDATE clients SET 
                       name = :name, 
                       company_name = :company_name, 
                       phone = :phone, 
                       email = :email, 
                       address = :address, 
                       gst_number = :gst_number, 
                       special_pricing = :special_pricing, 
                       is_active = :is_active, 
                       updated_at = NOW() 
                       WHERE id = :id");
            
            $db->bind(':name', $name);
            $db->bind(':company_name', $company_name);
            $db->bind(':phone', $phone);
            $db->bind(':email', $email);
            $db->bind(':address', $address);
            $db->bind(':gst_number', $gst_number);
            $db->bind(':special_pricing', $special_pricing);
            $db->bind(':is_active', $is_active);
            $db->bind(':id', $client_id);
            
            $db->execute();
            
            // Reset password if requested
            if ($reset_password) {
                $new_password = hashPassword('1234');
                $db->query("UPDATE clients SET password = :password, force_password_change = 1 WHERE id = :id");
                $db->bind(':password', $new_password);
                $db->bind(':id', $client_id);
                $db->execute();
                
                // Send WhatsApp notification about password reset
                $message = "Your password has been reset.\n";
                $message .= "New Password: 1234\n";
                $message .= "Please login and change your password immediately.\n";
                $message .= "Login: {$_SERVER['HTTP_HOST']}/auth/login.php";
                
                sendNotification($phone, $message, 'password_reset');
            }
            
            // Log activity
            logActivity($_SESSION['user_id'], 'client_updated', "Updated client: $name ({$client->client_code})");
            
            setFlashMessage('success', 'Client updated successfully!');
            header('Location: view.php?id=' . $client_id);
            exit();
            
        } catch (Exception $e) {
            $errors[] = 'Error updating client: ' . $e->getMessage();
        }
    }
} else {
    // Pre-fill form with existing data
    $_POST = [
        'name' => $client->name,
        'company_name' => $client->company_name,
        'phone' => $client->phone,
        'email' => $client->email,
        'address' => $client->address,
        'gst_number' => $client->gst_number,
        'special_pricing' => $client->special_pricing,
        'is_active' => $client->is_active
    ];
}

$page_title = 'Edit Client - ' . $client->name;
include '../../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../../includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-user-edit"></i> Edit Client</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="view.php?id=<?php echo $client->id; ?>" class="btn btn-sm btn-info">
                            <i class="fas fa-eye"></i> View Details
                        </a>
                        <a href="pricing.php?id=<?php echo $client->id; ?>" class="btn btn-sm btn-secondary">
                            <i class="fas fa-tags"></i> Special Pricing
                        </a>
                    </div>
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Clients
                    </a>
                </div>
            </div>

            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">
                                Edit Client Information - <?php echo $client->client_code; ?>
                            </h6>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="name" class="form-label">Client Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="name" name="name" 
                                               value="<?php echo htmlspecialchars($_POST['name']); ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="company_name" class="form-label">Company Name</label>
                                        <input type="text" class="form-control" id="company_name" name="company_name" 
                                               value="<?php echo htmlspecialchars($_POST['company_name']); ?>">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">Phone Number <span class="text-danger">*</span></label>
                                        <input type="tel" class="form-control" id="phone" name="phone" 
                                               value="<?php echo htmlspecialchars($_POST['phone']); ?>" 
                                               pattern="[0-9]{10}" maxlength="10" required>
                                        <div class="form-text">10-digit mobile number</div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">Email Address</label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="<?php echo htmlspecialchars($_POST['email']); ?>">
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="address" class="form-label">Address</label>
                                    <textarea class="form-control" id="address" name="address" rows="3"><?php echo htmlspecialchars($_POST['address']); ?></textarea>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="gst_number" class="form-label">GST Number</label>
                                        <input type="text" class="form-control" id="gst_number" name="gst_number" 
                                               value="<?php echo htmlspecialchars($_POST['gst_number']); ?>" 
                                               pattern="[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}" 
                                               placeholder="22AAAAA0000A1Z5">
                                        <div class="form-text">15-character GST number (optional)</div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="special_pricing" name="special_pricing" 
                                                   <?php echo $_POST['special_pricing'] ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="special_pricing">
                                                Enable Special Pricing
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                                   <?php echo $_POST['is_active'] ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="is_active">
                                                Active Status
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="reset_password" name="reset_password">
                                            <label class="form-check-label" for="reset_password">
                                                Reset Password to 1234
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="view.php?id=<?php echo $client->id; ?>" class="btn btn-secondary me-md-2">Cancel</a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> Update Client
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Client Statistics</h6>
                        </div>
                        <div class="card-body">
                            <?php $stats = getClientStats($client_id); ?>
                            <div class="row text-center">
                                <div class="col-6 mb-3">
                                    <h4 class="text-primary"><?php echo $stats['total_orders']; ?></h4>
                                    <small>Total Orders</small>
                                </div>
                                <div class="col-6 mb-3">
                                    <h4 class="text-warning"><?php echo $stats['pending_orders']; ?></h4>
                                    <small>Pending Orders</small>
                                </div>
                                <div class="col-6 mb-3">
                                    <h4 class="text-info"><?php echo $stats['total_invoices']; ?></h4>
                                    <small>Total Invoices</small>
                                </div>
                                <div class="col-6 mb-3">
                                    <h4 class="text-success"><?php echo $stats['cylinders_with_client']; ?></h4>
                                    <small>Cylinders</small>
                                </div>
                                <div class="col-12">
                                    <h5 class="<?php echo $stats['outstanding_amount'] > 0 ? 'text-danger' : 'text-success'; ?>">
                                        <?php echo formatCurrency($stats['outstanding_amount']); ?>
                                    </h5>
                                    <small>Outstanding Amount</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card shadow mt-3">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Important Notes</h6>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle"></i> Password Reset</h6>
                                <p class="mb-0">If you reset the password, the client will receive a WhatsApp notification with the new password (1234) and will be forced to change it on next login.</p>
                            </div>

                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> Status Change</h6>
                                <p class="mb-0">Deactivating a client will prevent them from logging in and placing new orders, but existing data will remain intact.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
// Phone number validation
document.getElementById('phone').addEventListener('input', function(e) {
    this.value = this.value.replace(/[^0-9]/g, '');
    if (this.value.length > 10) {
        this.value = this.value.slice(0, 10);
    }
});

// GST number formatting
document.getElementById('gst_number').addEventListener('input', function(e) {
    this.value = this.value.toUpperCase();
});

// Confirm password reset
document.getElementById('reset_password').addEventListener('change', function(e) {
    if (this.checked) {
        if (!confirm('Are you sure you want to reset the password? The client will receive a WhatsApp notification.')) {
            this.checked = false;
        }
    }
});
</script>

<?php include '../../includes/footer.php'; ?>
