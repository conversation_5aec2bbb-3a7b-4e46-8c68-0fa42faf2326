<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Check if user is logged in and has admin role
requireRole(['super_admin', 'office_admin']);

$errors = [];
$success = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $name = sanitize($_POST['name']);
    $company_name = sanitize($_POST['company_name']);
    $phone = sanitize($_POST['phone']);
    $email = sanitize($_POST['email']);
    $address = sanitize($_POST['address']);
    $gst_number = sanitize($_POST['gst_number']);
    $special_pricing = isset($_POST['special_pricing']) ? 1 : 0;
    $send_whatsapp = isset($_POST['send_whatsapp']) ? 1 : 0;
    
    // Validation
    if (empty($name)) {
        $errors[] = 'Client name is required.';
    }
    
    if (empty($phone)) {
        $errors[] = 'Phone number is required.';
    } elseif (!preg_match('/^[0-9]{10}$/', $phone)) {
        $errors[] = 'Phone number must be 10 digits.';
    }
    
    if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Invalid email format.';
    }
    
    // Check if phone already exists
    if (empty($errors)) {
        $db->query("SELECT id FROM clients WHERE phone = :phone");
        $db->bind(':phone', $phone);
        if ($db->single()) {
            $errors[] = 'Phone number already exists.';
        }
    }
    
    // Check if email already exists (if provided)
    if (empty($errors) && !empty($email)) {
        $db->query("SELECT id FROM clients WHERE email = :email");
        $db->bind(':email', $email);
        if ($db->single()) {
            $errors[] = 'Email already exists.';
        }
    }
    
    if (empty($errors)) {
        try {
            // Generate client code and default password
            $client_code = generateClientCode();
            $default_password = '1234';
            $hashed_password = hashPassword($default_password);
            
            // Insert client
            $db->query("INSERT INTO clients (client_code, name, company_name, phone, email, address, gst_number, password, special_pricing, created_by, created_at) 
                       VALUES (:client_code, :name, :company_name, :phone, :email, :address, :gst_number, :password, :special_pricing, :created_by, NOW())");
            
            $db->bind(':client_code', $client_code);
            $db->bind(':name', $name);
            $db->bind(':company_name', $company_name);
            $db->bind(':phone', $phone);
            $db->bind(':email', $email);
            $db->bind(':address', $address);
            $db->bind(':gst_number', $gst_number);
            $db->bind(':password', $hashed_password);
            $db->bind(':special_pricing', $special_pricing);
            $db->bind(':created_by', $_SESSION['user_id']);
            
            $db->execute();
            $client_id = $db->lastInsertId();
            
            // Log activity
            logActivity($_SESSION['user_id'], 'client_created', "Created client: $name ($client_code)");
            
            // Send WhatsApp notification if requested
            if ($send_whatsapp) {
                $company_settings = getCompanySettings();
                $message = "Welcome to {$company_settings->company_name}!\n\n";
                $message .= "Your account has been created:\n";
                $message .= "Client Code: $client_code\n";
                $message .= "Login: {$_SERVER['HTTP_HOST']}/auth/login.php\n";
                $message .= "Default Password: $default_password\n\n";
                $message .= "Please change your password on first login.\n";
                $message .= "Contact us for any assistance.";
                
                sendNotification($phone, $message, 'client_created');
            }
            
            setFlashMessage('success', "Client created successfully! Client Code: $client_code");
            header('Location: index.php');
            exit();
            
        } catch (Exception $e) {
            $errors[] = 'Error creating client: ' . $e->getMessage();
        }
    }
}

$page_title = 'Add New Client';
include '../../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../../includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-user-plus"></i> Add New Client</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Clients
                    </a>
                </div>
            </div>

            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Client Information</h6>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="name" class="form-label">Client Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="name" name="name" 
                                               value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="company_name" class="form-label">Company Name</label>
                                        <input type="text" class="form-control" id="company_name" name="company_name" 
                                               value="<?php echo isset($_POST['company_name']) ? htmlspecialchars($_POST['company_name']) : ''; ?>">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">Phone Number <span class="text-danger">*</span></label>
                                        <input type="tel" class="form-control" id="phone" name="phone" 
                                               value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>" 
                                               pattern="[0-9]{10}" maxlength="10" required>
                                        <div class="form-text">10-digit mobile number</div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">Email Address</label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="address" class="form-label">Address</label>
                                    <textarea class="form-control" id="address" name="address" rows="3"><?php echo isset($_POST['address']) ? htmlspecialchars($_POST['address']) : ''; ?></textarea>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="gst_number" class="form-label">GST Number</label>
                                        <input type="text" class="form-control" id="gst_number" name="gst_number" 
                                               value="<?php echo isset($_POST['gst_number']) ? htmlspecialchars($_POST['gst_number']) : ''; ?>" 
                                               pattern="[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}" 
                                               placeholder="22AAAAA0000A1Z5">
                                        <div class="form-text">15-character GST number (optional)</div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="special_pricing" name="special_pricing" 
                                                   <?php echo isset($_POST['special_pricing']) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="special_pricing">
                                                Enable Special Pricing
                                            </label>
                                            <div class="form-text">Allow custom pricing for this client</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="send_whatsapp" name="send_whatsapp" checked>
                                            <label class="form-check-label" for="send_whatsapp">
                                                Send WhatsApp Welcome Message
                                            </label>
                                            <div class="form-text">Send login credentials via WhatsApp</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="index.php" class="btn btn-secondary me-md-2">Cancel</a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> Create Client
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Important Notes</h6>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> Client Account Details</h6>
                                <ul class="mb-0">
                                    <li>Client code will be auto-generated (CLI001, CLI002, etc.)</li>
                                    <li>Default password: <strong>1234</strong></li>
                                    <li>Client must change password on first login</li>
                                    <li>Login URL: <code><?php echo $_SERVER['HTTP_HOST']; ?>/auth/login.php</code></li>
                                </ul>
                            </div>

                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle"></i> Special Pricing</h6>
                                <p class="mb-0">If enabled, you can set custom prices for different gas types for this client in the pricing section.</p>
                            </div>

                            <div class="alert alert-success">
                                <h6><i class="fab fa-whatsapp"></i> WhatsApp Integration</h6>
                                <p class="mb-0">Welcome message will include login credentials and instructions for first-time access.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
// Phone number validation
document.getElementById('phone').addEventListener('input', function(e) {
    this.value = this.value.replace(/[^0-9]/g, '');
    if (this.value.length > 10) {
        this.value = this.value.slice(0, 10);
    }
});

// GST number formatting
document.getElementById('gst_number').addEventListener('input', function(e) {
    this.value = this.value.toUpperCase();
});
</script>

<?php include '../../includes/footer.php'; ?>
