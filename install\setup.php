<?php
// Database Setup Script for Gas Cylinder Management System
// This script will create the database and tables

error_reporting(E_ALL);
ini_set('display_errors', 1);

$setup_complete = false;
$error_message = '';
$success_message = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $db_host = $_POST['db_host'] ?? 'localhost';
    $db_user = $_POST['db_user'] ?? 'root';
    $db_pass = $_POST['db_pass'] ?? '';
    $db_name = $_POST['db_name'] ?? 'gas_cylinder_management';
    
    $admin_username = $_POST['admin_username'] ?? 'admin';
    $admin_password = $_POST['admin_password'] ?? '';
    $admin_email = $_POST['admin_email'] ?? '';
    $admin_name = $_POST['admin_name'] ?? '';
    
    $company_name = $_POST['company_name'] ?? 'Sony Enterprises';
    $company_phone = $_POST['company_phone'] ?? '';
    $company_email = $_POST['company_email'] ?? '';
    $company_address = $_POST['company_address'] ?? '';
    
    try {
        // Connect to MySQL server (without database)
        $pdo = new PDO("mysql:host=$db_host;charset=utf8mb4", $db_user, $db_pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Create database
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE `$db_name`");
        
        // Read and execute schema
        $schema = file_get_contents('../database/schema.sql');
        
        // Remove the database creation and use statements from schema
        $schema = preg_replace('/CREATE DATABASE.*?;/i', '', $schema);
        $schema = preg_replace('/USE.*?;/i', '', $schema);
        
        // Split into individual statements
        $statements = array_filter(array_map('trim', explode(';', $schema)));
        
        foreach ($statements as $statement) {
            if (!empty($statement)) {
                $pdo->exec($statement);
            }
        }
        
        // Update company settings
        $stmt = $pdo->prepare("UPDATE company_settings SET 
            company_name = ?, 
            phone = ?, 
            email = ?, 
            address = ? 
            WHERE id = 1");
        $stmt->execute([$company_name, $company_phone, $company_email, $company_address]);
        
        // Update admin user
        $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("UPDATE users SET 
            username = ?, 
            email = ?, 
            password = ?, 
            full_name = ?, 
            phone = ? 
            WHERE id = 1");
        $stmt->execute([$admin_username, $admin_email, $hashed_password, $admin_name, $company_phone]);
        
        // Update database configuration file
        $config_content = "<?php
// Database configuration for Gas Cylinder Management System
define('DB_HOST', '$db_host');
define('DB_USER', '$db_user');
define('DB_PASS', '$db_pass');
define('DB_NAME', '$db_name');

class Database {
    private \$host = DB_HOST;
    private \$user = DB_USER;
    private \$pass = DB_PASS;
    private \$dbname = DB_NAME;
    private \$dbh;
    private \$error;

    public function __construct() {
        // Set DSN
        \$dsn = 'mysql:host=' . \$this->host . ';dbname=' . \$this->dbname . ';charset=utf8mb4';
        
        // Set options
        \$options = array(
            PDO::ATTR_PERSISTENT => true,
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => \"SET NAMES utf8mb4\"
        );

        // Create a new PDO instance
        try {
            \$this->dbh = new PDO(\$dsn, \$this->user, \$this->pass, \$options);
        } catch(PDOException \$e) {
            \$this->error = \$e->getMessage();
            die('Database connection failed: ' . \$this->error);
        }
    }

    // Get database connection
    public function getConnection() {
        return \$this->dbh;
    }

    // Prepare statement with query
    public function query(\$query) {
        \$this->stmt = \$this->dbh->prepare(\$query);
    }

    // Bind values
    public function bind(\$param, \$value, \$type = null) {
        if (is_null(\$type)) {
            switch (true) {
                case is_int(\$value):
                    \$type = PDO::PARAM_INT;
                    break;
                case is_bool(\$value):
                    \$type = PDO::PARAM_BOOL;
                    break;
                case is_null(\$value):
                    \$type = PDO::PARAM_NULL;
                    break;
                default:
                    \$type = PDO::PARAM_STR;
            }
        }
        \$this->stmt->bindValue(\$param, \$value, \$type);
    }

    // Execute the prepared statement
    public function execute() {
        return \$this->stmt->execute();
    }

    // Get result set as array of objects
    public function resultset() {
        \$this->execute();
        return \$this->stmt->fetchAll(PDO::FETCH_OBJ);
    }

    // Get single record as object
    public function single() {
        \$this->execute();
        return \$this->stmt->fetch(PDO::FETCH_OBJ);
    }

    // Get row count
    public function rowCount() {
        return \$this->stmt->rowCount();
    }

    // Get last insert ID
    public function lastInsertId() {
        return \$this->dbh->lastInsertId();
    }
}

// Create global database instance
\$db = new Database();
?>";
        
        file_put_contents('../config/database.php', $config_content);
        
        $setup_complete = true;
        $success_message = 'Database setup completed successfully! You can now login with your admin credentials.';
        
    } catch (Exception $e) {
        $error_message = 'Setup failed: ' . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup - Gas Cylinder Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        .setup-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        .setup-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="card setup-card">
                    <div class="card-header setup-header text-center py-4">
                        <h3><i class="fas fa-industry"></i> Sony Enterprises</h3>
                        <p class="mb-0">Gas Cylinder Management System - Setup</p>
                    </div>
                    <div class="card-body p-4">
                        <?php if ($error_message): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle"></i> <?php echo $error_message; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($success_message): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
                                <div class="mt-3">
                                    <a href="../auth/login.php" class="btn btn-success">
                                        <i class="fas fa-sign-in-alt"></i> Go to Login
                                    </a>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!$setup_complete): ?>
                        <form method="POST" action="">
                            <h5 class="mb-3"><i class="fas fa-database"></i> Database Configuration</h5>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="db_host" class="form-label">Database Host</label>
                                    <input type="text" class="form-control" id="db_host" name="db_host" value="localhost" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="db_name" class="form-label">Database Name</label>
                                    <input type="text" class="form-control" id="db_name" name="db_name" value="gas_cylinder_management" required>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="db_user" class="form-label">Database Username</label>
                                    <input type="text" class="form-control" id="db_user" name="db_user" value="root" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="db_pass" class="form-label">Database Password</label>
                                    <input type="password" class="form-control" id="db_pass" name="db_pass">
                                </div>
                            </div>
                            
                            <hr>
                            
                            <h5 class="mb-3"><i class="fas fa-user-shield"></i> Admin Account</h5>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="admin_username" class="form-label">Admin Username</label>
                                    <input type="text" class="form-control" id="admin_username" name="admin_username" value="admin" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="admin_password" class="form-label">Admin Password</label>
                                    <input type="password" class="form-control" id="admin_password" name="admin_password" required>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="admin_name" class="form-label">Admin Full Name</label>
                                    <input type="text" class="form-control" id="admin_name" name="admin_name" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="admin_email" class="form-label">Admin Email</label>
                                    <input type="email" class="form-control" id="admin_email" name="admin_email" required>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <h5 class="mb-3"><i class="fas fa-building"></i> Company Information</h5>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="company_name" class="form-label">Company Name</label>
                                    <input type="text" class="form-control" id="company_name" name="company_name" value="Sony Enterprises" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="company_phone" class="form-label">Company Phone</label>
                                    <input type="text" class="form-control" id="company_phone" name="company_phone" required>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="company_email" class="form-label">Company Email</label>
                                <input type="email" class="form-control" id="company_email" name="company_email" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="company_address" class="form-label">Company Address</label>
                                <textarea class="form-control" id="company_address" name="company_address" rows="3" required></textarea>
                            </div>
                            
                            <button type="submit" class="btn btn-primary w-100 py-2">
                                <i class="fas fa-cog"></i> Setup Database
                            </button>
                        </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
