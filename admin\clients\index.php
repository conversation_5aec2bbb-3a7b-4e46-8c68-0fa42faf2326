<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Check if user is logged in and has admin role
requireRole(['super_admin', 'office_admin']);

// Handle search and filters
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
$status_filter = isset($_GET['status']) ? sanitize($_GET['status']) : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$records_per_page = 25;

// Build query
$where_conditions = ['1=1'];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(c.name LIKE :search OR c.company_name LIKE :search OR c.phone LIKE :search OR c.email LIKE :search OR c.client_code LIKE :search)";
    $params[':search'] = "%$search%";
}

if (!empty($status_filter)) {
    $where_conditions[] = "c.is_active = :status";
    $params[':status'] = ($status_filter === 'active') ? 1 : 0;
}

$where_clause = implode(' AND ', $where_conditions);

// Get total count for pagination
$count_query = "SELECT COUNT(*) as total FROM clients c WHERE $where_clause";
$db->query($count_query);
foreach ($params as $key => $value) {
    $db->bind($key, $value);
}
$total_records = $db->single()->total;

// Calculate pagination
$pagination = paginate($total_records, $records_per_page, $page);

// Get clients
$query = "SELECT c.*, u.full_name as created_by_name,
          (SELECT COUNT(*) FROM orders WHERE client_id = c.id) as total_orders,
          (SELECT COUNT(*) FROM orders WHERE client_id = c.id AND status IN ('pending', 'assigned', 'refilling')) as pending_orders,
          (SELECT SUM(balance_amount) FROM invoices WHERE client_id = c.id AND status != 'paid') as outstanding_amount
          FROM clients c 
          LEFT JOIN users u ON c.created_by = u.id 
          WHERE $where_clause 
          ORDER BY c.created_at DESC 
          LIMIT {$pagination['limit']} OFFSET {$pagination['offset']}";

$db->query($query);
foreach ($params as $key => $value) {
    $db->bind($key, $value);
}
$clients = $db->resultset();

$page_title = 'Client Management';
include '../../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../../includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-users"></i> Client Management</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="create.php" class="btn btn-sm btn-primary">
                            <i class="fas fa-user-plus"></i> Add New Client
                        </a>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportToCSV('clientsTable', 'clients')">
                            <i class="fas fa-download"></i> Export CSV
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="printTable('clientsTable')">
                            <i class="fas fa-print"></i> Print
                        </button>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo htmlspecialchars($search); ?>" 
                                   placeholder="Name, company, phone, email, or code">
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">All Status</option>
                                <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>Active</option>
                                <option value="inactive" <?php echo $status_filter === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search"></i> Search
                            </button>
                            <a href="index.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> Clear
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        Total Clients
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php 
                                        $db->query("SELECT COUNT(*) as count FROM clients WHERE is_active = 1");
                                        echo $db->single()->count;
                                        ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-users fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        Active Orders
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php 
                                        $db->query("SELECT COUNT(*) as count FROM orders WHERE status IN ('pending', 'assigned', 'refilling')");
                                        echo $db->single()->count;
                                        ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        Outstanding Amount
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php 
                                        $db->query("SELECT SUM(balance_amount) as amount FROM invoices WHERE status != 'paid'");
                                        $result = $db->single();
                                        echo formatCurrency($result->amount ?? 0);
                                        ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-rupee-sign fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        Special Pricing
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php 
                                        $db->query("SELECT COUNT(DISTINCT client_id) as count FROM client_pricing WHERE is_active = 1");
                                        echo $db->single()->count;
                                        ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-tags fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Clients Table -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        Clients List (<?php echo $total_records; ?> total)
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="clientsTable">
                            <thead>
                                <tr>
                                    <th>Client Code</th>
                                    <th>Name</th>
                                    <th>Company</th>
                                    <th>Phone</th>
                                    <th>Email</th>
                                    <th>Orders</th>
                                    <th>Outstanding</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($clients as $client): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo $client->client_code; ?></strong>
                                        <?php if ($client->special_pricing): ?>
                                            <span class="badge bg-info ms-1">Special Pricing</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($client->name); ?></td>
                                    <td><?php echo htmlspecialchars($client->company_name ?? '-'); ?></td>
                                    <td>
                                        <?php echo $client->phone; ?>
                                        <a href="<?php echo generateWhatsAppURL($client->phone, 'Hello ' . $client->name . ', this is Sony Enterprises.'); ?>" 
                                           target="_blank" class="btn btn-sm btn-success ms-1" title="Send WhatsApp">
                                            <i class="fab fa-whatsapp"></i>
                                        </a>
                                    </td>
                                    <td><?php echo htmlspecialchars($client->email ?? '-'); ?></td>
                                    <td>
                                        <span class="badge bg-primary"><?php echo $client->total_orders; ?></span>
                                        <?php if ($client->pending_orders > 0): ?>
                                            <span class="badge bg-warning"><?php echo $client->pending_orders; ?> pending</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($client->outstanding_amount > 0): ?>
                                            <span class="text-danger"><?php echo formatCurrency($client->outstanding_amount); ?></span>
                                        <?php else: ?>
                                            <span class="text-success">₹0.00</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($client->is_active): ?>
                                            <span class="badge bg-success">Active</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">Inactive</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="view.php?id=<?php echo $client->id; ?>" class="btn btn-sm btn-info" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="edit.php?id=<?php echo $client->id; ?>" class="btn btn-sm btn-warning" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="pricing.php?id=<?php echo $client->id; ?>" class="btn btn-sm btn-secondary" title="Special Pricing">
                                                <i class="fas fa-tags"></i>
                                            </a>
                                            <?php if (hasRole('super_admin')): ?>
                                            <a href="delete.php?id=<?php echo $client->id; ?>" 
                                               class="btn btn-sm btn-danger delete-btn" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if ($pagination['total_pages'] > 1): ?>
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            <?php if ($pagination['has_previous']): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $pagination['current_page'] - 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status_filter); ?>">Previous</a>
                                </li>
                            <?php endif; ?>
                            
                            <?php for ($i = 1; $i <= $pagination['total_pages']; $i++): ?>
                                <li class="page-item <?php echo $i == $pagination['current_page'] ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status_filter); ?>"><?php echo $i; ?></a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($pagination['has_next']): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $pagination['current_page'] + 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status_filter); ?>">Next</a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include '../../includes/footer.php'; ?>
