    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // Initialize DataTables
        $(document).ready(function() {
            $('.data-table').DataTable({
                responsive: true,
                pageLength: 25,
                order: [[0, 'desc']],
                language: {
                    search: "Search:",
                    lengthMenu: "Show _MENU_ entries",
                    info: "Showing _START_ to _END_ of _TOTAL_ entries",
                    paginate: {
                        first: "First",
                        last: "Last",
                        next: "Next",
                        previous: "Previous"
                    }
                }
            });
        });
        
        // Auto-hide flash messages
        setTimeout(function() {
            $('.flash-message .alert').fadeOut();
        }, 5000);
        
        // Confirm delete actions
        $('.delete-btn').on('click', function(e) {
            if (!confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
                e.preventDefault();
            }
        });
        
        // WhatsApp share function
        function shareWhatsApp(phone, message) {
            const url = `https://wa.me/${phone}?text=${encodeURIComponent(message)}`;
            window.open(url, '_blank');
        }
        
        // Print function
        function printDiv(divId) {
            const printContent = document.getElementById(divId);
            const originalContent = document.body.innerHTML;
            document.body.innerHTML = printContent.innerHTML;
            window.print();
            document.body.innerHTML = originalContent;
            location.reload();
        }
        
        // Tank level animation
        function animateTankLevel(tankId, percentage) {
            const tank = document.getElementById(tankId);
            if (tank) {
                const fill = tank.querySelector('.tank-fill');
                if (fill) {
                    fill.style.height = percentage + '%';
                }
            }
        }
        
        // Initialize tank animations on page load
        document.addEventListener('DOMContentLoaded', function() {
            const tanks = document.querySelectorAll('.tank-animation');
            tanks.forEach(function(tank) {
                const percentage = tank.dataset.percentage || 0;
                setTimeout(function() {
                    animateTankLevel(tank.id, percentage);
                }, 500);
            });
        });
        
        // Form validation
        function validateForm(formId) {
            const form = document.getElementById(formId);
            const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
            let isValid = true;
            
            inputs.forEach(function(input) {
                if (!input.value.trim()) {
                    input.classList.add('is-invalid');
                    isValid = false;
                } else {
                    input.classList.remove('is-invalid');
                }
            });
            
            return isValid;
        }
        
        // Number formatting
        function formatCurrency(amount) {
            return new Intl.NumberFormat('en-IN', {
                style: 'currency',
                currency: 'INR'
            }).format(amount);
        }
        
        // Date formatting
        function formatDate(date) {
            return new Date(date).toLocaleDateString('en-IN');
        }
        
        // Mobile sidebar toggle
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebarMenu');
            sidebar.classList.toggle('show');
        }
        
        // QR Code Scanner functions
        let html5QrcodeScanner = null;
        
        function startQRScanner(elementId, onScanSuccess) {
            if (html5QrcodeScanner) {
                html5QrcodeScanner.clear();
            }
            
            html5QrcodeScanner = new Html5QrcodeScanner(
                elementId,
                { 
                    fps: 10, 
                    qrbox: { width: 250, height: 250 },
                    aspectRatio: 1.0
                },
                false
            );
            
            html5QrcodeScanner.render(onScanSuccess, function(error) {
                // Handle scan error
                console.warn(`QR Code scan error: ${error}`);
            });
        }
        
        function stopQRScanner() {
            if (html5QrcodeScanner) {
                html5QrcodeScanner.clear();
                html5QrcodeScanner = null;
            }
        }
        
        // Barcode scanner using camera
        function startBarcodeScanner(elementId, onScanSuccess) {
            // This would integrate with a barcode scanning library
            // For now, we'll use the same QR scanner
            startQRScanner(elementId, onScanSuccess);
        }
        
        // AJAX helper functions
        function ajaxRequest(url, method, data, successCallback, errorCallback) {
            $.ajax({
                url: url,
                method: method,
                data: data,
                dataType: 'json',
                success: function(response) {
                    if (successCallback) {
                        successCallback(response);
                    }
                },
                error: function(xhr, status, error) {
                    if (errorCallback) {
                        errorCallback(xhr, status, error);
                    } else {
                        console.error('AJAX Error:', error);
                        alert('An error occurred. Please try again.');
                    }
                }
            });
        }
        
        // Show loading spinner
        function showLoading(elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                element.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</div>';
            }
        }
        
        // Hide loading spinner
        function hideLoading(elementId, originalContent) {
            const element = document.getElementById(elementId);
            if (element) {
                element.innerHTML = originalContent;
            }
        }
        
        // Notification functions
        function showNotification(message, type = 'info') {
            const alertClass = `alert-${type}`;
            const notification = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            const container = document.querySelector('.flash-message') || document.body;
            container.insertAdjacentHTML('beforeend', notification);
            
            // Auto-hide after 5 seconds
            setTimeout(function() {
                const alerts = container.querySelectorAll('.alert');
                if (alerts.length > 0) {
                    alerts[alerts.length - 1].remove();
                }
            }, 5000);
        }
        
        // Export functions
        function exportToCSV(tableId, filename) {
            const table = document.getElementById(tableId);
            const rows = table.querySelectorAll('tr');
            let csv = [];
            
            rows.forEach(function(row) {
                const cols = row.querySelectorAll('td, th');
                const rowData = [];
                cols.forEach(function(col) {
                    rowData.push('"' + col.textContent.replace(/"/g, '""') + '"');
                });
                csv.push(rowData.join(','));
            });
            
            const csvContent = csv.join('\n');
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename + '.csv';
            a.click();
            window.URL.revokeObjectURL(url);
        }
        
        // Print table
        function printTable(tableId) {
            const table = document.getElementById(tableId);
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                <head>
                    <title>Print</title>
                    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
                    <style>
                        body { font-size: 12px; }
                        table { width: 100% !important; }
                        @media print {
                            .no-print { display: none !important; }
                        }
                    </style>
                </head>
                <body>
                    <div class="container-fluid">
                        <h3>Sony Enterprises - Gas Cylinder Management System</h3>
                        ${table.outerHTML}
                    </div>
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }
    </script>
    
    <!-- Page-specific scripts -->
    <?php if (isset($page_scripts)): ?>
        <?php echo $page_scripts; ?>
    <?php endif; ?>
</body>
</html>
