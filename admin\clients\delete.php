<?php
session_start();
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Check if user is logged in and has super admin role
requireRole(['super_admin']);

$client_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$client_id) {
    setFlashMessage('error', 'Invalid client ID.');
    header('Location: index.php');
    exit();
}

// Get client details
$db->query("SELECT * FROM clients WHERE id = :id");
$db->bind(':id', $client_id);
$client = $db->single();

if (!$client) {
    setFlashMessage('error', 'Client not found.');
    header('Location: index.php');
    exit();
}

// Check if client has any orders
$db->query("SELECT COUNT(*) as count FROM orders WHERE client_id = :client_id");
$db->bind(':client_id', $client_id);
$order_count = $db->single()->count;

// Check if client has any invoices
$db->query("SELECT COUNT(*) as count FROM invoices WHERE client_id = :client_id");
$db->bind(':client_id', $client_id);
$invoice_count = $db->single()->count;

// Check if client has any payments
$db->query("SELECT COUNT(*) as count FROM payments WHERE client_id = :client_id");
$db->bind(':client_id', $client_id);
$payment_count = $db->single()->count;

// Check if client has any cylinders
$db->query("SELECT COUNT(*) as count FROM cylinders WHERE current_client_id = :client_id");
$db->bind(':client_id', $client_id);
$cylinder_count = $db->single()->count;

$has_dependencies = ($order_count > 0 || $invoice_count > 0 || $payment_count > 0 || $cylinder_count > 0);

// Handle deletion
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['confirm_delete'])) {
    if ($has_dependencies) {
        setFlashMessage('error', 'Cannot delete client with existing orders, invoices, payments, or cylinders. Please deactivate instead.');
        header('Location: view.php?id=' . $client_id);
        exit();
    }
    
    try {
        // Start transaction
        $db->getConnection()->beginTransaction();
        
        // Delete client pricing
        $db->query("DELETE FROM client_pricing WHERE client_id = :client_id");
        $db->bind(':client_id', $client_id);
        $db->execute();
        
        // Delete client
        $db->query("DELETE FROM clients WHERE id = :id");
        $db->bind(':id', $client_id);
        $db->execute();
        
        // Commit transaction
        $db->getConnection()->commit();
        
        // Log activity
        logActivity($_SESSION['user_id'], 'client_deleted', "Deleted client: {$client->name} ({$client->client_code})");
        
        setFlashMessage('success', 'Client deleted successfully.');
        header('Location: index.php');
        exit();
        
    } catch (Exception $e) {
        // Rollback transaction
        $db->getConnection()->rollback();
        setFlashMessage('error', 'Error deleting client: ' . $e->getMessage());
        header('Location: view.php?id=' . $client_id);
        exit();
    }
}

$page_title = 'Delete Client - ' . $client->name;
include '../../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <?php include '../../includes/sidebar.php'; ?>
        
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-user-times text-danger"></i> Delete Client</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="view.php?id=<?php echo $client->id; ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Client
                    </a>
                </div>
            </div>

            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card shadow border-danger">
                        <div class="card-header bg-danger text-white">
                            <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Confirm Client Deletion</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-danger">
                                <h6><i class="fas fa-warning"></i> Warning: This action cannot be undone!</h6>
                                <p class="mb-0">You are about to permanently delete the following client and all associated data.</p>
                            </div>

                            <!-- Client Information -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">Client Information</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <table class="table table-borderless table-sm">
                                                <tr>
                                                    <td><strong>Client Code:</strong></td>
                                                    <td><?php echo $client->client_code; ?></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Name:</strong></td>
                                                    <td><?php echo htmlspecialchars($client->name); ?></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Company:</strong></td>
                                                    <td><?php echo htmlspecialchars($client->company_name ?? '-'); ?></td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div class="col-md-6">
                                            <table class="table table-borderless table-sm">
                                                <tr>
                                                    <td><strong>Phone:</strong></td>
                                                    <td><?php echo $client->phone; ?></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Email:</strong></td>
                                                    <td><?php echo htmlspecialchars($client->email ?? '-'); ?></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Created:</strong></td>
                                                    <td><?php echo formatDateTime($client->created_at); ?></td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Dependencies Check -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">Data Dependencies</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3 text-center">
                                            <div class="border rounded p-3 <?php echo $order_count > 0 ? 'border-danger bg-light' : 'border-success'; ?>">
                                                <h4 class="<?php echo $order_count > 0 ? 'text-danger' : 'text-success'; ?>">
                                                    <?php echo $order_count; ?>
                                                </h4>
                                                <small>Orders</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3 text-center">
                                            <div class="border rounded p-3 <?php echo $invoice_count > 0 ? 'border-danger bg-light' : 'border-success'; ?>">
                                                <h4 class="<?php echo $invoice_count > 0 ? 'text-danger' : 'text-success'; ?>">
                                                    <?php echo $invoice_count; ?>
                                                </h4>
                                                <small>Invoices</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3 text-center">
                                            <div class="border rounded p-3 <?php echo $payment_count > 0 ? 'border-danger bg-light' : 'border-success'; ?>">
                                                <h4 class="<?php echo $payment_count > 0 ? 'text-danger' : 'text-success'; ?>">
                                                    <?php echo $payment_count; ?>
                                                </h4>
                                                <small>Payments</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3 text-center">
                                            <div class="border rounded p-3 <?php echo $cylinder_count > 0 ? 'border-danger bg-light' : 'border-success'; ?>">
                                                <h4 class="<?php echo $cylinder_count > 0 ? 'text-danger' : 'text-success'; ?>">
                                                    <?php echo $cylinder_count; ?>
                                                </h4>
                                                <small>Cylinders</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <?php if ($has_dependencies): ?>
                                <!-- Cannot Delete -->
                                <div class="alert alert-danger">
                                    <h6><i class="fas fa-ban"></i> Cannot Delete Client</h6>
                                    <p>This client cannot be deleted because they have associated data (orders, invoices, payments, or cylinders).</p>
                                    <p class="mb-0"><strong>Recommendation:</strong> Instead of deleting, consider deactivating the client to prevent new orders while preserving historical data.</p>
                                </div>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                    <a href="edit.php?id=<?php echo $client->id; ?>" class="btn btn-warning">
                                        <i class="fas fa-user-slash"></i> Deactivate Client Instead
                                    </a>
                                    <a href="view.php?id=<?php echo $client->id; ?>" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left"></i> Cancel
                                    </a>
                                </div>
                            <?php else: ?>
                                <!-- Can Delete -->
                                <div class="alert alert-success">
                                    <h6><i class="fas fa-check-circle"></i> Safe to Delete</h6>
                                    <p class="mb-0">This client has no associated data and can be safely deleted.</p>
                                </div>

                                <form method="POST" action="">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="confirm_delete" name="confirm_delete" required>
                                        <label class="form-check-label" for="confirm_delete">
                                            <strong>I understand that this action cannot be undone and want to permanently delete this client.</strong>
                                        </label>
                                    </div>

                                    <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                        <button type="submit" class="btn btn-danger" id="deleteBtn" disabled>
                                            <i class="fas fa-trash"></i> Permanently Delete Client
                                        </button>
                                        <a href="view.php?id=<?php echo $client->id; ?>" class="btn btn-secondary">
                                            <i class="fas fa-times"></i> Cancel
                                        </a>
                                    </div>
                                </form>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
// Enable delete button only when checkbox is checked
document.getElementById('confirm_delete').addEventListener('change', function() {
    document.getElementById('deleteBtn').disabled = !this.checked;
});

// Additional confirmation on form submit
document.querySelector('form').addEventListener('submit', function(e) {
    if (!confirm('Are you absolutely sure you want to delete this client? This action cannot be undone!')) {
        e.preventDefault();
    }
});
</script>

<?php include '../../includes/footer.php'; ?>
